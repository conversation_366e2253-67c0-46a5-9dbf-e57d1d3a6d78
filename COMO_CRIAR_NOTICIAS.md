# 📰 Como Criar Notícias no Blog Kodiak

## 🚀 Processo Super Simples

### 1. **Instalar e Configurar** (uma vez só)
```bash
# Instalar dependências
npm install

# Configurar banco (ver INSTALACAO_BLOG.md)
npm run init-db
npm run seed-db

# Executar
npm run dev
```

### 2. **Fazer Login**
- Acesse: http://localhost:3000/blog
- Login: `<EMAIL>`
- Senha: `admin123`

### 3. **Acessar Painel Admin**
- Acesse: http://localhost:3000/blog/admin
- Ou adicione um botão no header para usuários logados

---

## 📝 Criando uma Notícia

### **Passo 1: Criar Apresentação no Gamma**
1. Acesse [gamma.app](https://gamma.app)
2. Crie uma apresentação sobre sua notícia
3. Adicione slides com:
   - Tí<PERSON><PERSON> da notícia
   - Conteúdo principal
   - <PERSON><PERSON>, gr<PERSON><PERSON><PERSON>, dados
   - Conclusões

### **Passo 2: Obter URL de Embed**
1. No Gamma, clique em **"Share"**
2. Selecione **"Embed"**
3. Copie a URL que aparece no `src` do iframe
4. Exemplo: `https://gamma.app/embed/0oy42pmm78wcnb2`

### **Passo 3: Preencher Formulário**
1. Acesse `/blog/admin`
2. Preencha:
   - **Título**: "Produtividade da Indústria em 2025"
   - **Resumo**: Breve descrição
   - **URL do Gamma**: Cole a URL do embed
   - **Imagem**: URL de uma imagem (opcional)
   - **Tags**: "Indústria 4.0", "Produtividade", etc.
   - **Publicar**: Ative para publicar imediatamente

### **Passo 4: Publicar**
- Clique em **"Criar Notícia"**
- A notícia aparecerá automaticamente no blog
- URL será: `/blog/titulo-da-noticia`

---

## 🎯 **Vantagens do Sistema**

### ✅ **Super Simples**
- Não precisa escrever HTML
- Não precisa configurar layout
- Gamma faz todo o design

### ✅ **Conteúdo Rico**
- Apresentações interativas
- Gráficos e animações
- Design profissional automático

### ✅ **Rápido de Implementar**
- 5 minutos para criar uma notícia
- Formulário simples
- Publicação instantânea

### ✅ **Responsivo**
- Funciona em mobile/desktop
- Iframe se adapta automaticamente
- Design consistente

---

## 📋 **Exemplos de URLs do Gamma**

```
✅ Correto:
https://gamma.app/embed/0oy42pmm78wcnb2
https://gamma.app/embed/abc123def456

❌ Incorreto:
https://gamma.app/docs/0oy42pmm78wcnb2
https://gamma.app/public/0oy42pmm78wcnb2
```

---

## 🔧 **Personalização Avançada**

### **Adicionar Botão Admin no Header**
Edite `components/ui/header.tsx` e adicione:

```tsx
{user && (
  <Link href="/blog/admin">
    <Button variant="ghost" size="sm">
      Admin
    </Button>
  </Link>
)}
```

### **Customizar Tamanho do Iframe**
Edite `app/blog/[slug]/page.tsx` linha do iframe:

```tsx
<iframe
  src={post.iframe_url}
  style={{ 
    width: '100%', 
    maxWidth: '100%', 
    height: '600px' // Altere aqui
  }}
  allow="fullscreen"
  title={post.title}
  className="rounded-lg border"
/>
```

---

## 🎨 **Dicas para Criar Boas Notícias**

### **No Gamma:**
1. **Use templates profissionais**
2. **Adicione dados e gráficos**
3. **Mantenha slides concisos**
4. **Use imagens de qualidade**
5. **Teste em mobile**

### **No Blog:**
1. **Títulos chamativos**
2. **Resumos informativos**
3. **Tags relevantes**
4. **Imagens destacadas**

---

## 📊 **Métricas Automáticas**

O sistema já rastreia:
- ✅ **Visualizações** (automático)
- ✅ **Likes** (usuários logados)
- ✅ **Compartilhamentos** (redes sociais)
- ✅ **Newsletter** (inscrições)

---

## 🚀 **Fluxo Completo de Exemplo**

1. **Gamma**: Criar apresentação "Automação Industrial 2025"
2. **Embed**: Copiar `https://gamma.app/embed/xyz123`
3. **Admin**: Preencher formulário
4. **Publicar**: Notícia fica disponível em `/blog/automacao-industrial-2025`
5. **Compartilhar**: Usuários podem curtir e compartilhar

---

## 🎯 **Resultado Final**

- ✅ Blog profissional e moderno
- ✅ Notícias ricas e interativas  
- ✅ Sistema de engajamento completo
- ✅ Fácil de usar e manter
- ✅ SEO otimizado
- ✅ Totalmente responsivo

**🎉 Pronto! Seu blog está funcionando perfeitamente!**
