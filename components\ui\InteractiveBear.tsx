"use client";

import React, { Suspense, useRef } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, useGLTF, Preload, Html } from '@react-three/drei';
import * as THREE from 'three';

// Componente que carrega e exibe o modelo do urso
function BearModel(props: any) {
  const group = useRef<THREE.Group>(null!);
  // Carrega o modelo GLB - Certifique-se de que o caminho está correto!
  // Você PRECISA colocar o arquivo kodiak_bear.glb em public/models/
  const { scene } = useGLTF('/models/kodiak_bear.glb');

  // Animação sutil (opcional, pode ser removida se não desejada)
  useFrame((state) => {
    if (group.current) {
      const t = state.clock.getElapsedTime();
      group.current.rotation.y = Math.sin(t / 4) / 4; // Leve balanço
      group.current.position.y = Math.sin(t / 2) / 10; // Leve movimento vertical
    }
  });

  // Ajuste a escala e posição conforme necessário para o seu modelo
  return (
    <group ref={group} {...props} dispose={null}>
      {/* Ajustado a posição Y de -0.8 para 0 para centralizar verticalmente */}
      <primitive object={scene} scale={0.8} position={[0, 0, 0]} />
    </group>
  );
}

// Pré-carrega o modelo para melhorar a experiência
useGLTF.preload('/models/kodiak_bear.glb');

// Componente Canvas que renderiza a cena do urso
export function InteractiveBear() {
  return (
    <div className="w-full h-[300px] md:h-[400px] rounded-xl overflow-hidden border border-blue-400/20 shadow-xl shadow-blue-500/10 bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative cursor-grab active:cursor-grabbing">
      <Canvas
        shadows // Habilita sombras para mais realismo
        camera={{ position: [2, 1, 4], fov: 50 }} // Ajuste a posição da câmera
        gl={{ preserveDrawingBuffer: true }}
      >
        {/* Luz ambiente */}
        <ambientLight intensity={Math.PI * 0.5} />
        {/* Luz direcional principal (simula sol) */}
        <directionalLight
          position={[5, 5, 5]}
          intensity={2}
          castShadow
          shadow-mapSize-width={1024}
          shadow-mapSize-height={1024}
        />
        {/* Luz pontual para realces */}
        <pointLight position={[-5, -5, -5]} intensity={50} color="#a78bfa" />
        {/* Chão simples para receber sombras (opcional) */}
        <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -0.8, 0]} receiveShadow>
          <planeGeometry args={[10, 10]} />
          <meshStandardMaterial color="#1e3a8a" transparent opacity={0.3} /> {/* Cor azul escura semi-transparente */}
        </mesh>

        {/* Suspense para mostrar um loader enquanto o modelo carrega */}
        <Suspense fallback={<Html center className="text-white">Carregando...</Html>}>
          <BearModel />
        </Suspense>

        {/* Controles de órbita */}
        <OrbitControls
          enableZoom={true}
          enablePan={true}
          maxPolarAngle={Math.PI / 2} // Limita a rotação vertical para não ver por baixo
          minPolarAngle={Math.PI / 4}
          minDistance={2} // Zoom mínimo
          maxDistance={6} // Zoom máximo
        />
        <Preload all />
      </Canvas>
      <div className="absolute inset-0 bg-gradient-to-t from-blue-900/40 to-transparent pointer-events-none"></div>
      <div className="absolute bottom-2 left-2 text-xs text-blue-300/70 pointer-events-none">
        Clique e arraste para girar
      </div>
    </div>
  );
}
