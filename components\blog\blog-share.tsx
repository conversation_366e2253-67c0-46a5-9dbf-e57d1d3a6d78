'use client';

import { useState } from 'react';
import { BlogPost } from '@/types/blog';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Share2, Facebook, Twitter, Linkedin, Copy, MessageCircle } from 'lucide-react';
import { toast } from 'sonner';

interface BlogShareProps {
  post: BlogPost;
}

export function BlogShare({ post }: BlogShareProps) {
  const [loading, setLoading] = useState(false);

  const postUrl = `${window.location.origin}/blog/${post.slug}`;
  const postTitle = post.title;
  const postDescription = post.excerpt || 'Confira este post no blog Kodiak';

  const trackShare = async (platform: string) => {
    try {
      await fetch(`/api/blog/posts/${post.slug}/share`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ platform }),
      });
    } catch (error) {
      console.error('Error tracking share:', error);
    }
  };

  const shareOptions = [
    {
      name: 'Facebook',
      icon: Facebook,
      action: () => {
        const url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(postUrl)}`;
        window.open(url, '_blank', 'width=600,height=400');
        trackShare('facebook');
      },
    },
    {
      name: 'Twitter',
      icon: Twitter,
      action: () => {
        const url = `https://twitter.com/intent/tweet?url=${encodeURIComponent(postUrl)}&text=${encodeURIComponent(postTitle)}`;
        window.open(url, '_blank', 'width=600,height=400');
        trackShare('twitter');
      },
    },
    {
      name: 'LinkedIn',
      icon: Linkedin,
      action: () => {
        const url = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(postUrl)}`;
        window.open(url, '_blank', 'width=600,height=400');
        trackShare('linkedin');
      },
    },
    {
      name: 'WhatsApp',
      icon: MessageCircle,
      action: () => {
        const text = `${postTitle}\n\n${postDescription}\n\n${postUrl}`;
        const url = `https://wa.me/?text=${encodeURIComponent(text)}`;
        window.open(url, '_blank');
        trackShare('whatsapp');
      },
    },
    {
      name: 'Copiar Link',
      icon: Copy,
      action: async () => {
        try {
          await navigator.clipboard.writeText(postUrl);
          toast.success('Link copiado para a área de transferência!');
          trackShare('copy');
        } catch (error) {
          console.error('Error copying to clipboard:', error);
          toast.error('Erro ao copiar link');
        }
      },
    },
  ];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center gap-2">
          <Share2 className="h-4 w-4" />
          <span className="hidden sm:inline">Compartilhar</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-48">
        {shareOptions.map((option) => (
          <DropdownMenuItem
            key={option.name}
            onClick={option.action}
            className="flex items-center gap-2 cursor-pointer"
          >
            <option.icon className="h-4 w-4" />
            {option.name}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
