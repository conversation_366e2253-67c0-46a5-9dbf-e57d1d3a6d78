import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/database';
import { verifyToken } from '@/lib/auth';

// GET - Buscar post por slug
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params;
    const token = request.cookies.get('auth-token')?.value;
    let userId = null;

    if (token) {
      const user = verifyToken(token);
      userId = user?.id;
    }

    // Buscar post com informações do autor e contagem de likes
    const postQuery = `
      SELECT 
        bp.*,
        u.name as author_name,
        u.email as author_email,
        COUNT(pl.id) as likes_count,
        ${userId ? `EXISTS(SELECT 1 FROM post_likes WHERE post_id = bp.id AND user_id = $2) as is_liked` : 'false as is_liked'}
      FROM blog_posts bp
      LEFT JOIN users u ON bp.author_id = u.id
      LEFT JOIN post_likes pl ON bp.id = pl.post_id
      WHERE bp.slug = $1 AND bp.published = true
      GROUP BY bp.id, u.name, u.email
    `;

    const queryParams = [slug];
    if (userId) queryParams.push(userId);

    const result = await query(postQuery, queryParams);

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Post não encontrado' },
        { status: 404 }
      );
    }

    const post = result.rows[0];

    // Incrementar views
    await query(
      'UPDATE blog_posts SET views = views + 1 WHERE id = $1',
      [post.id]
    );

    // Buscar posts relacionados (mesmas tags)
    let relatedPosts = [];
    if (post.tags && post.tags.length > 0) {
      const relatedQuery = `
        SELECT id, title, slug, excerpt, featured_image, created_at
        FROM blog_posts
        WHERE published = true 
        AND id != $1 
        AND tags && $2
        ORDER BY created_at DESC
        LIMIT 3
      `;
      
      const relatedResult = await query(relatedQuery, [post.id, post.tags]);
      relatedPosts = relatedResult.rows;
    }

    return NextResponse.json({
      success: true,
      data: {
        ...post,
        likes_count: parseInt(post.likes_count),
        views: post.views + 1, // Retornar o valor atualizado
        author: {
          name: post.author_name,
          email: post.author_email
        },
        related_posts: relatedPosts
      }
    });
  } catch (error) {
    console.error('Get post error:', error);
    return NextResponse.json(
      { success: false, message: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
