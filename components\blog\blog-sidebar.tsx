'use client';

import { useState, useEffect } from 'react';
import { BlogPost } from '@/types/blog';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Calendar, Eye, Heart, Mail } from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import Link from 'next/link';
import Image from 'next/image';
import { toast } from 'sonner';

interface BlogSidebarProps {
  onTagClick?: (tag: string) => void;
}

export function BlogSidebar({ onTagClick }: BlogSidebarProps) {
  const [popularPosts, setPopularPosts] = useState<BlogPost[]>([]);
  const [email, setEmail] = useState('');
  const [subscribing, setSubscribing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [subscribed, setSubscribed] = useState(false);

  useEffect(() => {
    fetchPopularPosts();
  }, []);

  const fetchPopularPosts = async () => {
    try {
      const response = await fetch('/api/blog/popular?limit=5');
      const data = await response.json();
      
      if (data.success) {
        setPopularPosts(data.data);
      }
    } catch (error) {
      console.error('Error fetching popular posts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleNewsletterSubscribe = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      toast.error('Por favor, insira seu email');
      return;
    }

    setSubscribing(true);

    try {
      const response = await fetch('/api/newsletter/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success(data.message);
        setEmail('');
        setSubscribed(true);
        // Resetar após 5 segundos
        setTimeout(() => setSubscribed(false), 5000);
      } else {
        toast.error(data.message);
      }
    } catch (error) {
      toast.error('Erro ao se inscrever na newsletter');
    } finally {
      setSubscribing(false);
    }
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'dd MMM', { locale: ptBR });
  };

  // Tags populares (simuladas - em produção viriam da API)
  const popularTags = [
    'Indústria 4.0',
    'ERP',
    'Automação',
    'Produtividade',
    'Gestão',
    'Tecnologia',
    'Inovação',
    'Qualidade'
  ];

  return (
    <div className="space-y-6">
      {/* Newsletter */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5 text-primary" />
            Newsletter
          </CardTitle>
        </CardHeader>
        <CardContent>
          {subscribed ? (
            <div className="text-center py-4">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Mail className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-green-800 mb-2">Inscrição Confirmada!</h3>
              <p className="text-sm text-green-600">
                Obrigado por se inscrever na nossa newsletter. Você receberá as últimas novidades em breve!
              </p>
            </div>
          ) : (
            <>
              <p className="text-sm text-gray-600 mb-4">
                Receba as últimas novidades e insights diretamente no seu email.
              </p>
              <form onSubmit={handleNewsletterSubscribe} className="space-y-3">
                <Input
                  type="email"
                  placeholder="Seu email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
                <Button
                  type="submit"
                  className="w-full bg-primary hover:bg-primary/90"
                  disabled={subscribing}
                >
                  {subscribing ? 'Inscrevendo...' : 'Inscrever-se'}
                </Button>
              </form>
            </>
          )}
        </CardContent>
      </Card>

      {/* Posts Populares */}
      <Card>
        <CardHeader>
          <CardTitle>Posts Populares</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                </div>
              ))}
            </div>
          ) : popularPosts.length > 0 ? (
            <div className="space-y-4">
              {popularPosts.map((post) => (
                <div key={post.id} className="group">
                  <Link href={`/blog/${post.slug}`}>
                    <div className="flex gap-3">
                      {post.featured_image && (
                        <div className="relative w-16 h-16 flex-shrink-0 rounded-lg overflow-hidden">
                          <Image
                            src={post.featured_image}
                            alt={post.title}
                            fill
                            className="object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                        </div>
                      )}
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium line-clamp-2 group-hover:text-primary transition-colors">
                          {post.title}
                        </h4>
                        <div className="flex items-center gap-3 mt-2 text-xs text-gray-500">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            <span>{formatDate(post.created_at)}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Eye className="h-3 w-3" />
                            <span>{post.views}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Heart className="h-3 w-3" />
                            <span>{post.likes_count || 0}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Link>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-gray-500">
              Nenhum post popular encontrado.
            </p>
          )}
        </CardContent>
      </Card>

      {/* Tags Populares */}
      <Card>
        <CardHeader>
          <CardTitle>Tags Populares</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {popularTags.map((tag) => (
              <Badge
                key={tag}
                variant="secondary"
                className="cursor-pointer hover:bg-primary hover:text-white transition-colors"
                onClick={() => onTagClick?.(tag)}
              >
                {tag}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* CTA Kodiak */}
      <Card className="bg-gradient-to-r from-[#1B1AFF] to-[#4A4AFF] text-white">
        <CardContent className="p-6 text-center">
          <h3 className="font-semibold mb-2">Conheça o Kodiak ERP</h3>
          <p className="text-sm text-blue-100 mb-4">
            Transforme sua gestão industrial com nossa solução completa.
          </p>
          <Button variant="secondary" size="sm" asChild>
            <Link href="/">
              Saiba Mais
            </Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
