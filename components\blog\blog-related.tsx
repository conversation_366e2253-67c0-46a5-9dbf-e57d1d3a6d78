'use client';

import { BlogPost } from '@/types/blog';
import { Card, CardContent } from '@/components/ui/card';
import { Calendar } from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import Link from 'next/link';
import Image from 'next/image';

interface BlogRelatedProps {
  posts: BlogPost[];
}

export function BlogRelated({ posts }: BlogRelatedProps) {
  if (posts.length === 0) return null;

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'dd MMM yyyy', { locale: ptBR });
  };

  return (
    <section className="mt-12 pt-8 border-t">
      <h2 className="text-2xl font-bold mb-6">Posts Relacionados</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {posts.map((post) => (
          <Card key={post.id} className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
            <Link href={`/blog/${post.slug}`}>
              {post.featured_image && (
                <div className="relative h-40 overflow-hidden rounded-t-lg">
                  <Image
                    src={post.featured_image}
                    alt={post.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                </div>
              )}
              
              <CardContent className="p-4">
                <div className="flex items-center gap-2 text-sm text-gray-500 mb-2">
                  <Calendar className="h-4 w-4" />
                  <span>{formatDate(post.created_at)}</span>
                </div>
                
                <h3 className="font-semibold line-clamp-2 group-hover:text-primary transition-colors">
                  {post.title}
                </h3>
                
                {post.excerpt && (
                  <p className="text-sm text-gray-600 line-clamp-2 mt-2">
                    {post.excerpt}
                  </p>
                )}
              </CardContent>
            </Link>
          </Card>
        ))}
      </div>
    </section>
  );
}
