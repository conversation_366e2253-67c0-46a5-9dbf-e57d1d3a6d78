"use client";

import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ModuleDetailsModal } from "@/components/ui/module-details-modal";
import { ArrowRight, ArrowLeft, BoxIcon, ShoppingCart, LineChart, Truck, TrendingUp } from "lucide-react";
import moduleData, { ModuleKey } from "@/data/module-data";
import gsap from "gsap";

export function ModulesSection() {
  const [activeModule, setActiveModule] = useState<ModuleKey>('inventory');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [previousModule, setPreviousModule] = useState<ModuleKey>('inventory');
  
  const moduleContentRef = useRef<HTMLDivElement>(null);
  const moduleIconRef = useRef<HTMLDivElement>(null);
  const moduleTitleRef = useRef<HTMLHeadingElement>(null);
  const moduleDescriptionRef = useRef<HTMLParagraphElement>(null);
  const moduleFeaturesRef = useRef<HTMLDivElement>(null);
  const backgroundRef = useRef<HTMLDivElement>(null);
  const previousBackgroundRef = useRef<HTMLDivElement>(null);
  
  const getModuleIcon = (key: string) => {
    switch(key) {
      case 'inventory': return <BoxIcon className="h-6 w-6 text-white" />;
      case 'purchasing': return <ShoppingCart className="h-6 w-6 text-white" />;
      case 'vendas': return <LineChart className="h-6 w-6 text-white" />;
      case 'logistica': return <Truck className="h-6 w-6 text-white" />;
      case 'analytics': return <TrendingUp className="h-6 w-6 text-white" />;
      default: return <BoxIcon className="h-6 w-6 text-white" />;
    }
  };
  
  const animateModuleChange = () => {
    if (isAnimating) return;
    
    setIsAnimating(true);
    
    const tl = gsap.timeline({
      onComplete: () => setIsAnimating(false)
    });
    
    // Animar saída dos elementos
    tl.to([moduleIconRef.current, moduleTitleRef.current], {
      opacity: 0,
      y: -20,
      duration: 0.2,
      ease: "power2.in"
    })
    .to([moduleDescriptionRef.current, moduleFeaturesRef.current], {
      opacity: 0,
      y: -15,
      duration: 0.2,
      ease: "power2.in",
      stagger: 0.1
    }, "-=0.2")
    
    // Animar entrada dos novos elementos
    .to([moduleIconRef.current, moduleTitleRef.current], {
      opacity: 1,
      y: 0,
      duration: 0.3,
      ease: "power2.out"
    })
    .to([moduleDescriptionRef.current, moduleFeaturesRef.current], {
      opacity: 1,
      y: 0,
      duration: 0.3,
      ease: "power2.out",
      stagger: 0.1
    }, "-=0.2");
  };
  
  // Animar entrada inicial
  useEffect(() => {
    const tl = gsap.timeline();
    
    // Animar o background inicial
    if (backgroundRef.current) {
      tl.fromTo(backgroundRef.current, 
        { scale: 1.1, opacity: 0 },
        { scale: 1, opacity: 1, duration: 1, ease: "power2.out" }
      );
    }
    
    tl.fromTo([moduleIconRef.current, moduleTitleRef.current], 
      { opacity: 0, y: 20 },
      { opacity: 1, y: 0, duration: 0.5, ease: "power2.out" }
    )
    .fromTo([moduleDescriptionRef.current, moduleFeaturesRef.current],
      { opacity: 0, y: 20 },
      { opacity: 1, y: 0, duration: 0.5, ease: "power2.out", stagger: 0.1 },
      "-=0.2"
    );
  }, []);
  
  // Animar mudança de módulo
  useEffect(() => {
    if (moduleContentRef.current) {
      animateModuleChange();
    }
    
    // Animar transição de background
    if (backgroundRef.current && previousBackgroundRef.current && activeModule !== previousModule) {
      // Configurar o background anterior
      gsap.set(previousBackgroundRef.current, {
        backgroundImage: `linear-gradient(to bottom, rgba(0,0,0,0.7), rgba(0,0,0,0.85)), url(${moduleData[previousModule]?.image})`,
        opacity: 1,
        scale: 1,
        zIndex: 1
      });
      
      // Configurar o novo background
      gsap.set(backgroundRef.current, {
        backgroundImage: `linear-gradient(to bottom, rgba(0,0,0,0.7), rgba(0,0,0,0.85)), url(${moduleData[activeModule]?.image})`,
        opacity: 0,
        scale: 1.05,
        zIndex: 2
      });
      
      // Animar a transição
      const tl = gsap.timeline();
      
      tl.to(previousBackgroundRef.current, {
        opacity: 0,
        scale: 0.95,
        duration: 0.8,
        ease: "power2.inOut"
      })
      .to(backgroundRef.current, {
        opacity: 1,
        scale: 1,
        duration: 0.8,
        ease: "power2.out"
      }, "-=0.8");
      
      // Atualizar o módulo anterior
      setPreviousModule(activeModule);
    }
  }, [activeModule, previousModule]);

  return (
    <section id="modules" className="relative min-h-screen py-16 md:py-24 overflow-hidden flex items-center justify-center">
      {/* Background Images */}
      <div
        ref={previousBackgroundRef}
        className="absolute inset-0"
        style={{
          backgroundImage: `linear-gradient(to bottom, rgba(0,0,0,0.7), rgba(0,0,0,0.85)), url(${moduleData[previousModule]?.image})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          willChange: 'opacity, transform',
        }}
      />
      <div
        ref={backgroundRef}
        className="absolute inset-0"
        style={{
          backgroundImage: `linear-gradient(to bottom, rgba(0,0,0,0.7), rgba(0,0,0,0.85)), url(${moduleData[activeModule]?.image})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          willChange: 'opacity, transform',
        }}
      />
      
      {/* Content */}
      <div className="relative w-full py-4 md:py-8 z-10">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto text-center">
            {/* Module Info Container */}
            <div ref={moduleContentRef} className="bg-black/50 backdrop-blur-md rounded-xl p-4 md:p-6 shadow-xl border border-white/10 relative z-10">
              {/* Icon and Title */}
              <div className="flex flex-col items-center mb-4">
                <div ref={moduleIconRef} className="rounded-full bg-white/10 p-3 mb-3">
                  {getModuleIcon(activeModule)}
                </div>
                <h2 ref={moduleTitleRef} className="text-2xl md:text-3xl font-bold text-white">
                  {moduleData[activeModule].title}
                </h2>
              </div>
              
              {/* Description */}
              <p ref={moduleDescriptionRef} className="text-base text-gray-200 mb-4">
                {moduleData[activeModule].description}
              </p>
              
              {/* Key Features - Sempre em duas colunas */}
              <div ref={moduleFeaturesRef} className="grid grid-cols-2 gap-2 mb-5 text-sm">
                {moduleData[activeModule].features.map((feature, index) => (
                  <div key={index} className="flex items-start gap-2 text-left">
                    <div className="rounded-full bg-white/20 p-1 flex-shrink-0 mt-0.5">
                      <ArrowRight className="h-3 w-3 text-white" />
                    </div>
                    <span className="text-gray-200">{feature}</span>
                  </div>
                ))}
              </div>
              
              {/* Button and Navigation */}
              <div className="flex flex-col items-center gap-4">
                <Button
                  size="default"
                  className="bg-white/20 text-white backdrop-blur-sm hover:bg-white/30"
                  onClick={() => setIsModalOpen(true)}
                >
                  Saiba mais
                </Button>
                
                {/* Navigation Controls */}
                <div className="flex items-center gap-4 w-full justify-between px-2 md:px-6 mt-2">
                  <button
                    onClick={() => {
                      if (isAnimating) return;
                      const keys = Object.keys(moduleData) as ModuleKey[];
                      const currentIndex = keys.indexOf(activeModule);
                      const prevIndex = currentIndex === 0 ? keys.length - 1 : currentIndex - 1;
                      setActiveModule(keys[prevIndex]);
                    }}
                    className="rounded-full bg-white/20 p-2 backdrop-blur-sm hover:bg-white/30 transition-colors"
                    disabled={isAnimating}
                  >
                    <ArrowLeft className="h-5 w-5 text-white" />
                  </button>
                  
                  <div className="flex-1 max-w-[300px]">
                    <div className="relative h-1.5 bg-white/20 rounded-full">
                      <div
                        className="absolute h-1.5 bg-white rounded-full transition-all duration-300"
                        style={{
                          width: `${(Object.keys(moduleData).indexOf(activeModule) + 1) * (100 / Object.keys(moduleData).length)}%`
                        }}
                      />
                    </div>
                  </div>
                  
                  <button
                    onClick={() => {
                      if (isAnimating) return;
                      const keys = Object.keys(moduleData) as ModuleKey[];
                      const currentIndex = keys.indexOf(activeModule);
                      const nextIndex = currentIndex === keys.length - 1 ? 0 : currentIndex + 1;
                      setActiveModule(keys[nextIndex]);
                    }}
                    className="rounded-full bg-white/20 p-2 backdrop-blur-sm hover:bg-white/30 transition-colors"
                    disabled={isAnimating}
                  >
                    <ArrowRight className="h-5 w-5 text-white" />
                  </button>
                </div>

                <div className="text-center text-base font-bold text-white mt-2">
                  {(Object.keys(moduleData).indexOf(activeModule) + 1).toString().padStart(2, '0')}
                  <span className="text-xs text-white/60">/{Object.keys(moduleData).length}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Module Details Modal */}
      <ModuleDetailsModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        module={moduleData[activeModule]}
      />
    </section>
  );
}
