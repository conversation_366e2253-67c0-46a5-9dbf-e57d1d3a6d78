# Blog Kodiak - Documentação

## 📋 Visão Geral

O Blog Kodiak é uma plataforma completa de blog integrada ao site da Kodiak ERP, desenvolvida com Next.js 13, PostgreSQL e um sistema completo de autenticação e interação.

## ✨ Funcionalidades

### 🔐 Sistema de Autenticação
- Registro e login de usuários
- Autenticação JWT com cookies httpOnly
- Proteção de rotas sensíveis

### 📝 Gestão de Posts
- Criação, edição e publicação de posts
- Sistema de slugs únicos
- Suporte a conteúdo HTML e iframes
- Upload de imagens destacadas
- Sistema de tags
- Contagem de visualizações

### 💝 Sistema de Interação
- Curtidas em posts (requer login)
- Sistema de compartilhamento social
- Newsletter com inscrição por email
- Comentários e engajamento

### 🔍 Busca e Navegação
- Busca em tempo real por título e conteúdo
- Filtros por tags
- Ordenação por data, popularidade e visualizações
- Paginação completa
- Posts relacionados

### 📊 Analytics e Métricas
- Contagem de visualizações
- Métricas de curtidas
- Tracking de compartilhamentos
- Posts populares na sidebar

## 🛠️ Tecnologias Utilizadas

- **Frontend**: Next.js 13, React, TypeScript
- **Styling**: TailwindCSS, Shadcn/ui
- **Backend**: Next.js API Routes
- **Banco de Dados**: PostgreSQL
- **Autenticação**: JWT, bcryptjs
- **Validação**: Zod
- **Animações**: Framer Motion, GSAP
- **Notificações**: Sonner

## 🚀 Configuração e Instalação

### 1. Pré-requisitos
- Node.js 18+
- PostgreSQL 12+
- npm ou yarn

### 2. Configuração do Banco de Dados

```bash
# Instalar PostgreSQL (Ubuntu/Debian)
sudo apt update
sudo apt install postgresql postgresql-contrib

# Criar banco de dados
sudo -u postgres createdb kodiak_blog

# Criar usuário (opcional)
sudo -u postgres createuser --interactive
```

### 3. Configuração do Projeto

```bash
# Instalar dependências
npm install

# Copiar arquivo de ambiente
cp .env.example .env.local

# Editar variáveis de ambiente
nano .env.local
```

### 4. Configurar Variáveis de Ambiente

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=kodiak_blog
DB_USER=postgres
DB_PASSWORD=sua_senha

# JWT Secret
JWT_SECRET=sua_chave_secreta_super_segura

# OpenAI (já existente)
OPENAI_API_KEY=sua_chave_openai

# Site URL
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

### 5. Inicializar Banco de Dados

```bash
# Criar tabelas
npm run init-db

# Popular com dados de exemplo
npm run seed-db
```

### 6. Executar o Projeto

```bash
# Modo desenvolvimento
npm run dev

# Acessar o blog
http://localhost:3000/blog
```

## 📁 Estrutura do Projeto

```
├── app/
│   ├── api/
│   │   ├── auth/          # APIs de autenticação
│   │   ├── blog/          # APIs do blog
│   │   └── newsletter/    # API da newsletter
│   ├── blog/              # Páginas do blog
│   │   ├── [slug]/        # Página de detalhes do post
│   │   └── page.tsx       # Página principal do blog
├── components/
│   ├── blog/              # Componentes específicos do blog
│   │   ├── blog-auth.tsx
│   │   ├── blog-card.tsx
│   │   ├── blog-like.tsx
│   │   ├── blog-search.tsx
│   │   ├── blog-share.tsx
│   │   └── blog-sidebar.tsx
├── hooks/
│   └── use-auth.ts        # Hook de autenticação
├── lib/
│   ├── auth.ts            # Funções de autenticação
│   └── database.ts        # Configuração do banco
├── scripts/
│   ├── init-db.js         # Script de inicialização
│   └── seed-db.js         # Script de dados de exemplo
└── types/
    └── blog.ts            # Tipos TypeScript
```

## 🔧 APIs Disponíveis

### Autenticação
- `POST /api/auth/login` - Login
- `POST /api/auth/register` - Registro
- `POST /api/auth/logout` - Logout
- `GET /api/auth/me` - Dados do usuário

### Blog
- `GET /api/blog/posts` - Listar posts
- `POST /api/blog/posts` - Criar post (auth)
- `GET /api/blog/posts/[slug]` - Detalhes do post
- `POST /api/blog/posts/[slug]/like` - Curtir/descurtir
- `POST /api/blog/posts/[slug]/share` - Compartilhar
- `GET /api/blog/popular` - Posts populares

### Newsletter
- `POST /api/newsletter/subscribe` - Inscrever na newsletter

## 👤 Usuário Padrão

Após executar `npm run seed-db`:
- **Email**: <EMAIL>
- **Senha**: admin123

## 🎨 Customização

### Cores e Tema
As cores seguem o design system do Kodiak:
- Primary: `#1B1AFF`
- Gradients: `from-[#1B1AFF] to-[#4A4AFF]`

### Componentes
Todos os componentes utilizam Shadcn/ui e são totalmente customizáveis.

## 📱 Responsividade

O blog é totalmente responsivo e otimizado para:
- Desktop (1200px+)
- Tablet (768px - 1199px)
- Mobile (320px - 767px)

## 🔒 Segurança

- Senhas hasheadas com bcryptjs
- JWT tokens em cookies httpOnly
- Validação de dados com Zod
- Proteção contra SQL injection
- Rate limiting (recomendado para produção)

## 🚀 Deploy

### Preparação para Produção
1. Configure variáveis de ambiente de produção
2. Configure banco PostgreSQL em produção
3. Execute `npm run build`
4. Configure HTTPS
5. Configure backup do banco

### Variáveis de Produção
```env
NODE_ENV=production
JWT_SECRET=chave_super_segura_producao
DB_HOST=seu_host_producao
# ... outras variáveis
```

## 📈 Próximas Funcionalidades

- [ ] Sistema de comentários
- [ ] Categorias de posts
- [ ] Editor WYSIWYG
- [ ] Upload de imagens
- [ ] SEO otimizado
- [ ] RSS Feed
- [ ] Analytics avançado
- [ ] Moderação de conteúdo

## 🐛 Troubleshooting

### Erro de Conexão com Banco
```bash
# Verificar se PostgreSQL está rodando
sudo systemctl status postgresql

# Reiniciar PostgreSQL
sudo systemctl restart postgresql
```

### Erro de Permissões
```bash
# Dar permissões ao usuário
sudo -u postgres psql
GRANT ALL PRIVILEGES ON DATABASE kodiak_blog TO seu_usuario;
```

## 📞 Suporte

Para dúvidas ou problemas:
1. Verifique os logs do console
2. Confirme configurações do banco
3. Verifique variáveis de ambiente
4. Consulte a documentação do Next.js

---

**Desenvolvido com ❤️ para Kodiak ERP**
