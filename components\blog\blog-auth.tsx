'use client';

import { useState } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { User, LogOut } from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';

export function BlogAuth() {
  const { user, login, register, logout, loading } = useAuth();
  const [loginData, setLoginData] = useState({ email: '', password: '' });
  const [registerData, setRegisterData] = useState({ email: '', password: '', name: '' });
  const [authLoading, setAuthLoading] = useState(false);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setAuthLoading(true);

    try {
      await login(loginData);
      toast.success('Login realizado com sucesso!');
      setLoginData({ email: '', password: '' });
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Erro ao fazer login');
    } finally {
      setAuthLoading(false);
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setAuthLoading(true);

    try {
      await register(registerData);
      toast.success('Conta criada com sucesso!');
      setRegisterData({ email: '', password: '', name: '' });
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Erro ao criar conta');
    } finally {
      setAuthLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      toast.success('Logout realizado com sucesso!');
    } catch (error) {
      toast.error('Erro ao fazer logout');
    }
  };

  if (loading) {
    return (
      <Card className="mb-8">
        <CardContent className="p-6 text-center">
          <div className="animate-pulse">Carregando...</div>
        </CardContent>
      </Card>
    );
  }

  if (user) {
    return (
      <Card className="mb-8 bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                <User className="h-5 w-5 text-white" />
              </div>
              <div>
                <p className="font-medium">Olá, {user.name || user.email}!</p>
                <p className="text-sm text-gray-600">Você está logado e pode curtir posts.</p>
              </div>
            </div>
            <Button variant="outline" size="sm" onClick={handleLogout}>
              <LogOut className="h-4 w-4 mr-2" />
              Sair
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mb-8 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
      <CardContent className="p-6 text-center">
        <div className="mb-4">
          <User className="h-12 w-12 mx-auto text-primary mb-3" />
          <h3 className="text-xl font-semibold mb-2">Entre para Interagir</h3>
          <p className="text-gray-600 mb-6">
            Faça login para curtir posts, comentar e acessar conteúdo exclusivo
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Link href="/blog/login">
            <Button className="w-full sm:w-auto">
              Fazer Login
            </Button>
          </Link>
          <Link href="/blog/login">
            <Button variant="outline" className="w-full sm:w-auto">
              Criar Conta
            </Button>
          </Link>
        </div>

        <div className="mt-4 p-3 bg-white rounded-lg border text-sm">
          <strong>Conta de teste:</strong> <EMAIL> / admin123
        </div>
      </CardContent>
    </Card>
  );
}
