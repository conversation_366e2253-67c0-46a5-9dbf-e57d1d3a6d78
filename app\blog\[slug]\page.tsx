'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Header } from '@/components/ui/header';
import { Footer } from '@/components/ui/footer';
import { BlogPost } from '@/types/blog';
import { BlogSidebar } from '@/components/blog/blog-sidebar';
import { BlogShare } from '@/components/blog/blog-share';
import { BlogLike } from '@/components/blog/blog-like';
import { BlogRelated } from '@/components/blog/blog-related';
import { BlogAuth } from '@/components/blog/blog-auth';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, Eye, User, ArrowLeft } from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import Link from 'next/link';
import Image from 'next/image';
import { AuthProvider } from '@/hooks/use-auth';
import { Loader2 } from 'lucide-react';

export default function BlogPostPage() {
  const params = useParams();
  const slug = params.slug as string;
  
  const [post, setPost] = useState<BlogPost | null>(null);
  const [relatedPosts, setRelatedPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (slug) {
      fetchPost();
    }
  }, [slug]);

  const fetchPost = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/blog/posts/${slug}`);
      const data = await response.json();

      if (data.success) {
        setPost(data.data);
        setRelatedPosts(data.data.related_posts || []);
      } else {
        setError(data.message || 'Post não encontrado');
      }
    } catch (error) {
      console.error('Error fetching post:', error);
      setError('Erro ao carregar o post');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "dd 'de' MMMM 'de' yyyy", { locale: ptBR });
  };

  if (loading) {
    return (
      <AuthProvider>
        <div className="flex min-h-screen flex-col bg-gradient-to-br from-gray-50 via-white to-gray-100">
          <Header />
          <main className="flex-1 flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin" />
          </main>
          <Footer />
        </div>
      </AuthProvider>
    );
  }

  if (error || !post) {
    return (
      <AuthProvider>
        <div className="flex min-h-screen flex-col bg-gradient-to-br from-gray-50 via-white to-gray-100">
          <Header />
          <main className="flex-1 container mx-auto px-4 py-20 text-center">
            <h1 className="text-4xl font-bold mb-4">Post não encontrado</h1>
            <p className="text-gray-600 mb-8">{error}</p>
            <Link href="/blog">
              <Button>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Voltar ao Blog
              </Button>
            </Link>
          </main>
          <Footer />
        </div>
      </AuthProvider>
    );
  }

  return (
    <AuthProvider>
      <div className="flex min-h-screen flex-col bg-gradient-to-br from-gray-50 via-white to-gray-100">
        <Header />
        
        <main className="flex-1 container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Content Area */}
            <article className="lg:col-span-3">
              {/* Back Button */}
              <div className="mb-6">
                <Link href="/blog">
                  <Button variant="ghost" size="sm">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Voltar ao Blog
                  </Button>
                </Link>
              </div>

              {/* Featured Image */}
              {post.featured_image && (
                <div className="relative h-64 md:h-96 mb-8 rounded-xl overflow-hidden">
                  <Image
                    src={post.featured_image}
                    alt={post.title}
                    fill
                    className="object-cover"
                    priority
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                </div>
              )}

              {/* Post Header */}
              <header className="mb-8">
                <h1 className="text-3xl md:text-4xl font-bold mb-4 sora">
                  {post.title}
                </h1>
                
                <div className="flex flex-wrap items-center gap-4 text-gray-600 mb-6">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    <span>{formatDate(post.created_at)}</span>
                  </div>
                  
                  {post.author?.name && (
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      <span>{post.author.name}</span>
                    </div>
                  )}
                  
                  <div className="flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    <span>{post.views} visualizações</span>
                  </div>
                </div>

                {/* Tags */}
                {post.tags && post.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2 mb-6">
                    {post.tags.map((tag) => (
                      <Badge key={tag} variant="secondary">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                )}

                {/* Like and Share */}
                <div className="flex items-center gap-4 mb-8">
                  <BlogLike post={post} onUpdate={setPost} />
                  <BlogShare post={post} />
                </div>
              </header>

              {/* Post Content */}
              <div className="prose prose-lg max-w-none mb-8">
                {post.iframe_url ? (
                  <div className="mb-8">
                    <iframe
                      src={post.iframe_url}
                      style={{ width: '100%', maxWidth: '100%', height: '450px' }}
                      allow="fullscreen"
                      title={post.title}
                      className="rounded-lg border"
                    />
                  </div>
                ) : null}
                
                {post.content && (
                  <div 
                    className="prose prose-lg max-w-none"
                    dangerouslySetInnerHTML={{ __html: post.content }}
                  />
                )}
              </div>

              {/* Auth Section */}
              <BlogAuth />

              {/* Related Posts */}
              {relatedPosts.length > 0 && (
                <BlogRelated posts={relatedPosts} />
              )}
            </article>

            {/* Sidebar */}
            <aside className="lg:col-span-1">
              <BlogSidebar />
            </aside>
          </div>
        </main>

        <Footer />
      </div>
    </AuthProvider>
  );
}
