@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

.sora {
  font-family: var(--font-sora), serif;
  font-optical-sizing: auto;
  font-weight: 800;
  font-style: normal;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Responsive Media Queries */
@media screen and (max-width: 320px) {
  .container {
    @apply px-2;
  }
  h1 {
    @apply text-2xl;
  }
  h2 {
    @apply text-xl;
  }
}

@media screen and (min-width: 321px) and (max-width: 480px) {
  .container {
    @apply px-3;
  }
  h1 {
    @apply text-3xl;
  }
  h2 {
    @apply text-2xl;
  }
}

@media screen and (min-width: 481px) and (max-width: 768px) {
  .container {
    @apply px-4;
  }
  h1 {
    @apply text-4xl;
  }
  h2 {
    @apply text-3xl;
  }
  .mb-hero {
    margin-bottom: 4px;
  }
}

@media screen and (min-width: 769px) and (max-width: 1024px) {
  .container {
    @apply px-6;
  }
}

@media screen and (min-width: 1025px) and (max-width: 1200px) {
  .container {
    @apply px-8;
  }
}

@media screen and (min-width: 1201px) and (max-width: 1400px) {
  .container {
    @apply px-10;
  }
}

@media screen and (min-width: 1401px) {
  .container {
    @apply px-12 max-w-[1400px];
  }
}

/* Modern Gradients */
.gradient-primary {
  @apply bg-gradient-to-r from-[#FF416C] to-[#FF4B2B];
}

.gradient-secondary {
  @apply bg-gradient-to-r from-[#1B1AFF] to-[#4A4AFF];
}

/* Animations */
.hover-scale {
  @apply transition-transform duration-300 hover:scale-105;
}

.hover-lift {
  @apply transition-all duration-300 hover:-translate-y-1 hover:shadow-lg;
}

@keyframes pulse-x {
  0%, 100% {
    transform: translateX(0);
    opacity: 1;
  }
  50% {
    transform: translateX(-10px);
    opacity: 0.5;
  }
}

.animate-pulse-x {
  animation: pulse-x 1.5s ease-in-out infinite;
}

/* Performance Optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

/* Icon Gradients */
.icon-gradient {
  @apply relative overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-r before:from-[#FF416C] before:to-[#FF4B2B] before:opacity-0 before:transition-opacity hover:before:opacity-100;
}