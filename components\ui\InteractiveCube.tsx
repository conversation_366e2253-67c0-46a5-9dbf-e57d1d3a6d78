"use client"; // Necessário para hooks do React como useState e useRef

import React, { useRef, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Box, Text } from '@react-three/drei';
import * as THREE from 'three';

// Componente para uma única face do cubo com texto
function CubeFace({ position, rotation, text, color }: { position: [number, number, number], rotation: [number, number, number], text: string, color: string }) {
  return (
    <mesh position={position} rotation={rotation}>
      <planeGeometry args={[0.9, 0.9]} /> {/* Um pouco menor que a face do cubo */}
      <meshStandardMaterial color={color} side={THREE.DoubleSide} transparent opacity={0.8} />
      <Text
        position={[0, 0, 0.01]} // Ligeiramente à frente do plano
        fontSize={0.15}
        color="white"
        anchorX="center"
        anchorY="middle"
        maxWidth={0.8}
        textAlign="center"
      >
        {text}
      </Text>
    </mesh>
  );
}

// Componente principal do Cubo Interativo
function CubeMesh() {
  const meshRef = useRef<THREE.Group>(null!); // Corrigido para THREE.Group
  const [hovered, setHover] = useState(false);
  const [active, setActive] = useState(false);

  // Animação de rotação contínua
  useFrame((state, delta) => {
    if (meshRef.current && !active) { // Só rotaciona se não estiver sendo interagido
       meshRef.current.rotation.x += delta * 0.1;
       meshRef.current.rotation.y += delta * 0.15;
    }
  });

  // Textos para cada face
  const faceTexts = [
    "Módulos", "Dados", "Processos",
    "Análise", "Segurança", "Suporte"
  ];
  const faceColors = [
    "#60a5fa", // blue-400
    "#818cf8", // indigo-400
    "#a78bfa", // purple-400
    "#34d399", // emerald-400
    "#fbbf24", // amber-400
    "#f87171"  // red-400
  ];

  return (
    <group ref={meshRef}>
      {/* Caixa base invisível para referência e interação */}
      <Box
        args={[1, 1, 1]} // Tamanho do cubo
        onClick={() => setActive(!active)}
        onPointerOver={() => setHover(true)}
        onPointerOut={() => setHover(false)}
        scale={active ? 1.1 : 1} // Aumenta um pouco ao clicar
      >
        <meshStandardMaterial visible={false} /> {/* Material invisível */}
      </Box>

      {/* Faces do Cubo */}
      <CubeFace position={[0, 0, 0.5]} rotation={[0, 0, 0]} text={faceTexts[0]} color={faceColors[0]} /> {/* Frente */}
      <CubeFace position={[0, 0, -0.5]} rotation={[0, Math.PI, 0]} text={faceTexts[1]} color={faceColors[1]} /> {/* Trás */}
      <CubeFace position={[0.5, 0, 0]} rotation={[0, Math.PI / 2, 0]} text={faceTexts[2]} color={faceColors[2]} /> {/* Direita */}
      <CubeFace position={[-0.5, 0, 0]} rotation={[0, -Math.PI / 2, 0]} text={faceTexts[3]} color={faceColors[3]} /> {/* Esquerda */}
      <CubeFace position={[0, 0.5, 0]} rotation={[-Math.PI / 2, 0, 0]} text={faceTexts[4]} color={faceColors[4]} /> {/* Cima */}
      <CubeFace position={[0, -0.5, 0]} rotation={[Math.PI / 2, 0, 0]} text={faceTexts[5]} color={faceColors[5]} /> {/* Baixo */}
    </group>
  );
}

// Componente Canvas que renderiza a cena
export function InteractiveCube() {
  return (
    <div className="w-full h-[300px] md:h-[400px] rounded-xl overflow-hidden border border-blue-400/20 shadow-xl shadow-blue-500/10 bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative">
       <Canvas camera={{ position: [0, 0, 2.5], fov: 50 }}>
        {/* Luz ambiente suave */}
        <ambientLight intensity={Math.PI / 2} />
        {/* Luz direcional para sombras e realces */}
        <directionalLight position={[3, 2, 4]} intensity={3} />
        {/* Luz pontual para brilho adicional */}
        <pointLight position={[-3, -2, -4]} intensity={2} color="#a78bfa" />

        {/* O cubo interativo */}
        <CubeMesh />

        {/* Controles de órbita para permitir rotação pelo usuário */}
        <OrbitControls enableZoom={false} enablePan={false} autoRotate={false} />
      </Canvas>
       {/* Overlay para escurecer um pouco e dar contraste */}
       <div className="absolute inset-0 bg-gradient-to-t from-blue-900/40 to-transparent pointer-events-none"></div>
       {/* Texto informativo */}
       <div className="absolute bottom-2 left-2 text-xs text-blue-300/70 pointer-events-none">
         Clique e arraste para girar
       </div>
    </div>
  );
}
