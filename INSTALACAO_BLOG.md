# 🚀 Instalação do Blog Kodiak

## ⚡ Instalação Rápida

### 1. Instalar Dependências
```bash
npm install
```

### 2. Configurar Banco PostgreSQL

#### Windows (usando PostgreSQL installer):
1. Baixe e instale PostgreSQL: https://www.postgresql.org/download/windows/
2. Durante a instalação, defina uma senha para o usuário `postgres`
3. Abra pgAdmin ou psql e crie o banco:
```sql
CREATE DATABASE kodiak_blog;
```

#### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo -u postgres createdb kodiak_blog
```

#### macOS (usando Homebrew):
```bash
brew install postgresql
brew services start postgresql
createdb kodiak_blog
```

### 3. Configurar Variáveis de Ambiente
```bash
# Copiar arquivo de exemplo
cp .env.example .env.local

# Editar o arquivo .env.local com suas configurações
```

**Conteúdo do .env.local:**
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=kodiak_blog
DB_USER=postgres
DB_PASSWORD=sua_senha_postgres

# JWT Secret (gere uma chave segura)
JWT_SECRET=sua_chave_super_secreta_aqui

# OpenAI (já existente)
OPENAI_API_KEY=sua_chave_openai

# Site URL
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

### 4. Inicializar e Popular Banco
```bash
# Criar tabelas automaticamente
npm run init-db

# Popular com dados de exemplo
npm run seed-db
```

### 5. Executar o Projeto
```bash
npm run dev
```

### 6. Acessar o Blog
- **Blog**: http://localhost:3000/blog
- **Login**: <EMAIL> / admin123

---

## 🔧 Configuração Detalhada

### Configuração do PostgreSQL

#### Verificar se PostgreSQL está rodando:
```bash
# Linux/macOS
sudo systemctl status postgresql

# Windows (no Services ou cmd)
net start postgresql-x64-13
```

#### Conectar ao PostgreSQL:
```bash
# Linux/macOS
sudo -u postgres psql

# Windows
psql -U postgres
```

#### Comandos úteis no psql:
```sql
-- Listar bancos
\l

-- Conectar ao banco
\c kodiak_blog

-- Listar tabelas
\dt

-- Sair
\q
```

### Configuração de Produção

#### Variáveis de Ambiente de Produção:
```env
NODE_ENV=production
JWT_SECRET=chave_muito_segura_para_producao
DB_HOST=seu_host_de_producao
DB_PASSWORD=senha_segura_producao
NEXT_PUBLIC_SITE_URL=https://seudominio.com
```

#### Build para Produção:
```bash
npm run build
npm start
```

---

## 🐛 Solução de Problemas

### Erro: "database does not exist"
```bash
# Criar o banco manualmente
sudo -u postgres createdb kodiak_blog
```

### Erro: "password authentication failed"
```bash
# Redefinir senha do postgres
sudo -u postgres psql
ALTER USER postgres PASSWORD 'nova_senha';
```

### Erro: "connection refused"
```bash
# Verificar se PostgreSQL está rodando
sudo systemctl start postgresql

# Verificar porta
sudo netstat -plunt | grep postgres
```

### Erro: "permission denied"
```bash
# Dar permissões ao usuário
sudo -u postgres psql
GRANT ALL PRIVILEGES ON DATABASE kodiak_blog TO seu_usuario;
```

### Erro: "Cannot find module"
```bash
# Reinstalar dependências
rm -rf node_modules package-lock.json
npm install
```

---

## 📱 Funcionalidades Implementadas

✅ **Sistema de Autenticação**
- Registro e login de usuários
- JWT com cookies httpOnly
- Proteção de rotas

✅ **Gestão de Posts**
- Criação e visualização de posts
- Sistema de slugs únicos
- Suporte a iframes (Gamma)
- Tags e categorização

✅ **Interação Social**
- Sistema de likes (requer login)
- Compartilhamento em redes sociais
- Newsletter com inscrição

✅ **Busca e Navegação**
- Busca em tempo real
- Filtros por tags
- Ordenação por data/popularidade
- Paginação

✅ **Interface Responsiva**
- Design moderno e responsivo
- Sidebar com posts populares
- Integração com design Kodiak

---

## 🎯 Próximos Passos

1. **Testar todas as funcionalidades**
2. **Criar posts de exemplo**
3. **Configurar backup do banco**
4. **Implementar monitoramento**
5. **Configurar SSL em produção**

---

## 📞 Suporte

Se encontrar problemas:

1. **Verifique os logs**: `npm run dev` mostra erros no console
2. **Confirme o banco**: Teste conexão com `psql`
3. **Verifique .env.local**: Todas as variáveis estão corretas?
4. **Reinstale dependências**: `rm -rf node_modules && npm install`

---

**🎉 Blog Kodiak instalado com sucesso!**

Acesse http://localhost:3000/blog e comece a usar!
