'use client';

import { useState, useEffect } from 'react';
import { Header } from '@/components/ui/header';
import { Footer } from '@/components/ui/footer';
import { BlogPost, BlogResponse } from '@/types/blog';
import { BlogCard } from '@/components/blog/blog-card';
import { BlogSidebar } from '@/components/blog/blog-sidebar';
import { BlogSearch } from '@/components/blog/blog-search';
import { BlogPagination } from '@/components/blog/blog-pagination';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { AuthProvider } from '@/hooks/use-auth';
import Link from 'next/link';

export default function BlogPage() {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedTag, setSelectedTag] = useState('');
  const [sortBy, setSortBy] = useState<'latest' | 'popular' | 'views'>('latest');

  const fetchPosts = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '9',
        sort: sortBy,
      });

      if (searchQuery) params.append('query', searchQuery);
      if (selectedTag) params.append('tag', selectedTag);

      console.log('Fetching posts with params:', params.toString());

      const response = await fetch(`/api/blog/posts?${params}`);
      const data = await response.json();

      console.log('Posts response:', data);

      if (data.success) {
        setPosts(data.data.posts);
        setTotalPages(data.data.totalPages);
      } else {
        console.error('Error in posts response:', data);
      }
    } catch (error) {
      console.error('Error fetching posts:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPosts();
  }, [currentPage, searchQuery, selectedTag, sortBy]);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1);
  };

  const handleTagFilter = (tag: string) => {
    setSelectedTag(tag);
    setCurrentPage(1);
  };

  const handleSortChange = (sort: 'latest' | 'popular' | 'views') => {
    setSortBy(sort);
    setCurrentPage(1);
  };

  return (
    <AuthProvider>
      <div className="flex min-h-screen flex-col bg-gradient-to-br from-gray-50 via-white to-gray-100">
        <Header />
        
        {/* Hero Section */}
        <section className="relative pt-20 pb-16 bg-gradient-to-r from-[#1B1AFF] to-[#4A4AFF] text-white">
          <div className="container mx-auto px-4">
            <div className="text-center max-w-4xl mx-auto">
              <h1 className="text-4xl md:text-6xl font-bold mb-6 sora">
                Blog Kodiak
              </h1>
              <p className="text-xl md:text-2xl mb-8 text-blue-100">
                Insights, tendências e inovações para a indústria moderna
              </p>
              <p className="text-lg text-blue-200 max-w-2xl mx-auto mb-8">
                Fique por dentro das últimas novidades em gestão industrial,
                tecnologia e produtividade com nossos especialistas.
              </p>
              <Link href="/blog/login">
                <Button variant="secondary" size="lg" className="bg-white text-primary hover:bg-gray-100">
                  Fazer Login para Interagir
                </Button>
              </Link>
            </div>
          </div>
          <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
        </section>

        {/* Main Content */}
        <main className="flex-1 container mx-auto px-4 py-12">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Content Area */}
            <div className="lg:col-span-3">
              {/* Search and Filters */}
              <div className="mb-8">
                <BlogSearch onSearch={handleSearch} />
                
                <div className="flex flex-wrap gap-4 mt-6">
                  <div className="flex gap-2">
                    <Button
                      variant={sortBy === 'latest' ? 'default' : 'outline'}
                      onClick={() => handleSortChange('latest')}
                      size="sm"
                    >
                      Mais Recentes
                    </Button>
                    <Button
                      variant={sortBy === 'popular' ? 'default' : 'outline'}
                      onClick={() => handleSortChange('popular')}
                      size="sm"
                    >
                      Populares
                    </Button>
                    <Button
                      variant={sortBy === 'views' ? 'default' : 'outline'}
                      onClick={() => handleSortChange('views')}
                      size="sm"
                    >
                      Mais Vistos
                    </Button>
                  </div>
                  
                  {selectedTag && (
                    <Button
                      variant="secondary"
                      onClick={() => handleTagFilter('')}
                      size="sm"
                    >
                      Limpar filtro: {selectedTag} ✕
                    </Button>
                  )}
                </div>
              </div>

              {/* Posts Grid */}
              {loading ? (
                <div className="flex justify-center items-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : posts.length > 0 ? (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
                    {posts.map((post) => (
                      <BlogCard 
                        key={post.id} 
                        post={post} 
                        onTagClick={handleTagFilter}
                      />
                    ))}
                  </div>
                  
                  <BlogPagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={setCurrentPage}
                  />
                </>
              ) : (
                <div className="text-center py-12">
                  <p className="text-gray-500 text-lg">
                    {searchQuery || selectedTag 
                      ? 'Nenhum post encontrado com os filtros aplicados.' 
                      : 'Nenhum post encontrado.'
                    }
                  </p>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <BlogSidebar onTagClick={handleTagFilter} />
            </div>
          </div>
        </main>

        <Footer />
      </div>
    </AuthProvider>
  );
}
