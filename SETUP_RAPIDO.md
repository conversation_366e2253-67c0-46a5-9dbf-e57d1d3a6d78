# ⚡ Setup Rápido - Blog Kodiak

## 🚀 Instalação em 5 Minutos

### 1. Instalar Dependências
```bash
npm install
```

### 2. Configurar PostgreSQL

#### Windows:
1. Baixe PostgreSQL: https://www.postgresql.org/download/windows/
2. Instale com senha: `postgres`
3. Abra pgAdmin e crie banco: `kodiak_blog`

#### Linux:
```bash
sudo apt install postgresql
sudo -u postgres createdb kodiak_blog
```

### 3. Configurar Ambiente
```bash
# Copiar arquivo
cp .env.example .env.local

# Editar .env.local
DB_HOST=localhost
DB_PORT=5432
DB_NAME=kodiak_blog
DB_USER=postgres
DB_PASSWORD=postgres
JWT_SECRET=minha_chave_super_secreta
```

### 4. Inicializar
```bash
npm run init-db
npm run seed-db
npm run dev
```

### 5. Acessar
- **Blog**: http://localhost:3000/blog
- **Admin**: http://localhost:3000/blog/admin
- **Login**: <EMAIL> / admin123

---

## 📝 Criar Primeira Notícia

### 1. Fazer Login
- Acesse `/blog` e faça login
- Aparecerá botão "Admin" no header

### 2. Criar no Gamma
- Acesse [gamma.app](https://gamma.app)
- Crie apresentação sobre sua notícia
- Clique "Share" → "Embed"
- Copie URL: `https://gamma.app/embed/xyz123`

### 3. Publicar
- Acesse `/blog/admin`
- Preencha formulário:
  - **Título**: "Minha Primeira Notícia"
  - **URL Gamma**: Cole a URL
  - **Tags**: "Teste", "Primeira"
  - **Publicar**: ✅ Ativado
- Clique "Criar Notícia"

### 4. Ver Resultado
- Acesse `/blog`
- Sua notícia estará lá!
- URL: `/blog/minha-primeira-noticia`

---

## ✅ Funcionalidades Prontas

- ✅ **Sistema de posts** com iframes
- ✅ **Autenticação** completa
- ✅ **Likes** (requer login)
- ✅ **Compartilhamento** social
- ✅ **Newsletter** signup
- ✅ **Busca** em tempo real
- ✅ **Tags** e filtros
- ✅ **Posts populares**
- ✅ **Design responsivo**
- ✅ **Admin panel** simples

---

## 🐛 Problemas Comuns

### Erro "Cannot find module 'dotenv'"
```bash
npm install dotenv
```

### Erro "database does not exist"
```bash
sudo -u postgres createdb kodiak_blog
```

### Erro "connection refused"
```bash
sudo systemctl start postgresql
```

---

## 🎯 Próximos Passos

1. **Teste todas as funcionalidades**
2. **Crie mais notícias**
3. **Customize o design** se necessário
4. **Configure backup** do banco
5. **Deploy em produção**

---

**🎉 Blog Kodiak funcionando perfeitamente!**

Agora você tem um blog completo, moderno e fácil de usar!
