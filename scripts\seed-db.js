const { Pool } = require('pg');
const bcrypt = require('bcryptjs');
require('dotenv').config({ path: '.env.local' });

const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || '**************',
  database: process.env.DB_NAME || 'blog_bruno',
  password: process.env.DB_PASSWORD || '123!asd',
  port: parseInt(process.env.DB_PORT || '5432'),
});

const samplePosts = [
  {
    title: 'Produtividade da Indústria em 2025: Desafios e Oportunidades',
    content:
      '<p>A indústria moderna enfrenta desafios únicos em 2025. Com a evolução tecnológica acelerada, as empresas precisam se adaptar rapidamente para manter a competitividade.</p><p>Neste artigo, exploramos as principais tendências e como o Kodiak ERP pode ajudar sua empresa a navegar por essas mudanças.</p>',
    iframe_url: 'https://gamma.app/embed/0oy42pmm78wcnb2',
    excerpt:
      'Descubra como a indústria está se transformando em 2025 e quais são as principais oportunidades de crescimento.',
    tags: ['Indústria 4.0', 'Produtividade', 'Tecnologia'],
    featured_image:
      'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=800&h=400&fit=crop',
  },
  {
    title: 'Como Implementar um ERP na sua Indústria: Guia Completo',
    content:
      '<p>A implementação de um sistema ERP é um marco importante para qualquer indústria. Este guia completo mostra o passo a passo para uma implementação bem-sucedida.</p><p>Desde o planejamento inicial até o go-live, cobrimos todos os aspectos essenciais.</p>',
    iframe_url: 'https://gamma.app/embed/exemplo-implementacao-erp',
    excerpt:
      'Guia passo a passo para implementar um ERP industrial com sucesso e maximizar os resultados.',
    tags: ['ERP', 'Implementação', 'Gestão'],
    featured_image:
      'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=400&fit=crop',
  },
  {
    title: 'Automação Industrial: O Futuro da Manufatura',
    content:
      '<p>A automação industrial está revolucionando a forma como produzimos. Robôs, IA e IoT estão transformando as linhas de produção.</p><p>Veja como sua empresa pode se beneficiar dessas tecnologias emergentes.</p>',
    iframe_url: 'https://gamma.app/embed/exemplo-automacao',
    excerpt:
      'Explore as tecnologias de automação que estão moldando o futuro da manufatura industrial.',
    tags: ['Automação', 'Inovação', 'Manufatura'],
    featured_image:
      'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=400&fit=crop',
  },
  {
    title: 'Gestão de Qualidade na Era Digital',
    content:
      '<p>A qualidade sempre foi fundamental na indústria, mas as ferramentas digitais estão revolucionando como gerenciamos e garantimos a qualidade dos produtos.</p><p>Descubra as melhores práticas e tecnologias para gestão de qualidade moderna.</p>',
    excerpt:
      'Como as tecnologias digitais estão transformando a gestão de qualidade industrial.',
    tags: ['Qualidade', 'Digital', 'Gestão'],
    featured_image:
      'https://images.unsplash.com/photo-1586864387967-d02ef85d93e8?w=800&h=400&fit=crop',
  },
  {
    title: 'Sustentabilidade na Indústria: Práticas e Benefícios',
    content:
      '<p>A sustentabilidade não é mais opcional na indústria moderna. Empresas que adotam práticas sustentáveis não apenas ajudam o meio ambiente, mas também reduzem custos e melhoram sua imagem.</p><p>Conheça as principais práticas sustentáveis para sua indústria.</p>',
    excerpt:
      'Práticas sustentáveis que geram valor para sua indústria e para o meio ambiente.',
    tags: ['Sustentabilidade', 'Meio Ambiente', 'Eficiência'],
    featured_image:
      'https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?w=800&h=400&fit=crop',
  },
];

async function seedDatabase() {
  const client = await pool.connect();

  try {
    console.log('🌱 Populando banco de dados com dados de exemplo...');

    // Criar usuário admin
    const hashedPassword = await bcrypt.hash('admin123', 12);
    const userResult = await client.query(
      'INSERT INTO users (email, password_hash, name) VALUES ($1, $2, $3) RETURNING id',
      ['<EMAIL>', hashedPassword, 'Admin Kodiak'],
    );
    const adminId = userResult.rows[0].id;
    console.log('✅ Usuário admin criado');

    // Criar posts de exemplo
    for (const post of samplePosts) {
      const slug = post.title
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();

      await client.query(
        `INSERT INTO blog_posts 
         (title, slug, content, iframe_url, author_id, published, featured_image, excerpt, tags, views)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
        [
          post.title,
          slug,
          post.content,
          post.iframe_url,
          adminId,
          true,
          post.featured_image,
          post.excerpt,
          post.tags,
          Math.floor(Math.random() * 1000) + 50, // Views aleatórias
        ],
      );
    }
    console.log('✅ Posts de exemplo criados');

    // Adicionar alguns likes aleatórios
    const posts = await client.query('SELECT id FROM blog_posts');
    for (const post of posts.rows) {
      const likesCount = Math.floor(Math.random() * 20) + 1;
      for (let i = 0; i < likesCount; i++) {
        try {
          await client.query(
            'INSERT INTO post_likes (post_id, user_id) VALUES ($1, $2)',
            [post.id, adminId],
          );
        } catch (error) {
          // Ignorar erros de duplicata
        }
      }
    }
    console.log('✅ Likes de exemplo adicionados');

    // Adicionar alguns subscribers da newsletter
    const sampleEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ];

    for (const email of sampleEmails) {
      try {
        await client.query(
          'INSERT INTO newsletter_subscribers (email) VALUES ($1)',
          [email],
        );
      } catch (error) {
        // Ignorar erros de duplicata
      }
    }
    console.log('✅ Subscribers da newsletter adicionados');

    console.log('🎉 Banco de dados populado com sucesso!');
    console.log('\n📝 Dados criados:');
    console.log('- Usuário admin: <EMAIL> / admin123');
    console.log(`- ${samplePosts.length} posts de exemplo`);
    console.log('- Likes e views aleatórios');
    console.log('- Subscribers da newsletter');
    console.log('\n🚀 Acesse: http://localhost:3000/blog');
  } catch (error) {
    console.error('❌ Erro ao popular banco de dados:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  seedDatabase().catch(console.error);
}

module.exports = { seedDatabase };
