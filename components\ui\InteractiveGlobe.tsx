"use client";

import React, { Suspense, useRef, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Sphere, useTexture, Html, Preload } from '@react-three/drei';
import * as THREE from 'three';

// Função para converter coordenadas lat/long para posição 3D na esfera
function latLongToVector3(lat: number, lon: number, radius: number): THREE.Vector3 {
  // Convertendo para radianos
  const phi = (90 - lat) * (Math.PI / 180);
  const theta = (lon + 180) * (Math.PI / 180);

  // Calculando a posição 3D na superfície da esfera
  const x = -radius * Math.sin(phi) * Math.cos(theta);
  const y = radius * Math.cos(phi);
  const z = radius * Math.sin(phi) * Math.sin(theta);

  // Adicionando um pequeno deslocamento para fora da superfície para evitar z-fighting
  const direction = new THREE.Vector3(x, y, z).normalize();
  const finalPosition = new THREE.Vector3(
    x + direction.x * 0.02,
    y + direction.y * 0.02,
    z + direction.z * 0.02
  );

  console.log(`Ponto criado em lat: ${lat}, lon: ${lon} => posição 3D: [${finalPosition.x.toFixed(2)}, ${finalPosition.y.toFixed(2)}, ${finalPosition.z.toFixed(2)}]`);

  return finalPosition;
}

// Componente para um ponto de conexão no globo
function ConnectionPoint({ position, color, size = 0.03, pulse = true }: {
  position: THREE.Vector3,
  color: string,
  size?: number,
  pulse?: boolean
}) {
  const meshRef = useRef<THREE.Mesh>(null!);

  // Log para verificar se o componente está sendo renderizado
  console.log(`Renderizando ponto em: [${position.x.toFixed(2)}, ${position.y.toFixed(2)}, ${position.z.toFixed(2)}], tamanho: ${size}`);

  useFrame(({ clock }) => {
    if (meshRef.current && pulse) {
      // Efeito de pulsação mais pronunciado
      const scale = 1 + Math.sin(clock.getElapsedTime() * 2) * 0.5;
      meshRef.current.scale.set(scale, scale, scale);
    }
  });

  // Aumentamos o tamanho dos pontos DRASTICAMENTE para garantir visibilidade
  const actualSize = size * 10; // 10x maior que o original

  return (
    <group position={position}>
      {/* Esfera principal - muito maior e mais brilhante */}
      <mesh ref={meshRef}>
        <sphereGeometry args={[actualSize, 16, 16]} />
        <meshBasicMaterial // Usando meshBasicMaterial para máximo brilho
          color={color}
          toneMapped={false}
        />
      </mesh>

      {/* Halo ao redor do ponto para maior visibilidade */}
      <mesh>
        <sphereGeometry args={[actualSize * 2, 16, 16]} />
        <meshBasicMaterial
          color={color}
          transparent={true}
          opacity={0.4}
          toneMapped={false}
        />
      </mesh>

      {/* Luz pontual em cada ponto para destacá-lo */}
      <pointLight
        color={color}
        intensity={10}
        distance={2}
      />
    </group>
  );
}

// Componente para uma linha de conexão entre dois pontos
function ConnectionLine({ start, end, color, width = 1 }: {
  start: THREE.Vector3,
  end: THREE.Vector3,
  color: string,
  width?: number
}) {
  // Log para verificar se o componente está sendo renderizado
  console.log(`Renderizando linha de ${start.toArray()} para ${end.toArray()}`);

  // Aumentamos a largura DRASTICAMENTE para garantir visibilidade
  const actualWidth = width * 0.15; // 3x mais grosso

  // Criamos uma curva entre os dois pontos
  const points = [start, end];
  const curve = new THREE.CatmullRomCurve3(points);

  // Criamos um tubo ao longo da curva
  const tubeGeometry = new THREE.TubeGeometry(curve, 64, actualWidth, 8, false);

  // Material brilhante para o tubo - usando BasicMaterial para máximo brilho
  const tubeMaterial = new THREE.MeshBasicMaterial({
    color: color,
    transparent: true,
    opacity: 0.9
  });

  const tubeRef = useRef<THREE.Mesh>(null!);

  useFrame(({ clock }) => {
    if (tubeRef.current && tubeRef.current.material) {
      // Efeito de pulsação na opacidade da linha - mais pronunciado
      const opacity = (Math.sin(clock.getElapsedTime() * 3) + 1) / 2 * 0.5 + 0.5;
      (tubeRef.current.material as THREE.MeshBasicMaterial).opacity = opacity;
    }
  });

  // Adicionamos partículas ao longo da linha para efeito de fluxo de dados
  const particlesCount = 5; // Número de partículas por linha
  const particles = [];

  for (let i = 0; i < particlesCount; i++) {
    particles.push(i / particlesCount);
  }

  return (
    <group>
      {/* Tubo principal */}
      <mesh ref={tubeRef} geometry={tubeGeometry} material={tubeMaterial} />

      {/* Partículas ao longo da linha */}
      {particles.map((t, i) => (
        <ParticleOnLine
          key={`particle-${i}`}
          curve={curve}
          initialT={t}
          color={color}
          size={actualWidth * 2}
        />
      ))}
    </group>
  );
}

// Componente para uma partícula que se move ao longo de uma linha
function ParticleOnLine({
  curve,
  initialT,
  color,
  size
}: {
  curve: THREE.CatmullRomCurve3,
  initialT: number,
  color: string,
  size: number
}) {
  const meshRef = useRef<THREE.Mesh>(null!);
  const [t, setT] = useState(initialT);

  useFrame((_state) => {
    if (meshRef.current) {
      // Movimento da partícula ao longo da curva
      const newT = (t + 0.005) % 1;
      setT(newT);

      // Atualiza a posição
      const position = curve.getPointAt(newT);
      meshRef.current.position.copy(position);
    }
  });

  return (
    <mesh ref={meshRef} position={curve.getPointAt(initialT)}>
      <sphereGeometry args={[size, 8, 8]} />
      <meshBasicMaterial color={color} />
    </mesh>
  );
}

// Componente do Globo
function GlobeMesh() {
  const globeRef = useRef<THREE.Mesh>(null!);
  // Carrega a textura do mapa mundi
  const mapTexture = useTexture('/textures/mundo3.jpg');

  // Configuração da textura para melhor qualidade
  if (mapTexture) {
    mapTexture.anisotropy = 16;
    mapTexture.needsUpdate = true;
  }

  // Desativamos a rotação automática para facilitar a visualização
  // useFrame((_state, delta) => {
  //   if (globeRef.current) {
  //     // Rotaciona mais devagar para um efeito sutil
  //     globeRef.current.rotation.y += delta * 0.05;
  //   }
  // });

  // Raio do globo
  const radius = 1.5;

  // Pontos de conexão (cidades/clientes no Brasil) - [latitude, longitude]
  const connectionPoints = [
    { lat: -23.5505, lon: -46.6333, name: "São Paulo", color: "#60a5fa", size: 0.06 },      // São Paulo
    { lat: -22.9068, lon: -43.1729, name: "Rio de Janeiro", color: "#60a5fa", size: 0.05 }, // Rio de Janeiro
    { lat: -15.7801, lon: -47.9292, name: "Brasília", color: "#60a5fa", size: 0.05 },       // Brasília
    { lat: -25.4290, lon: -49.2671, name: "Curitiba", color: "#60a5fa", size: 0.04 },       // Curitiba
    { lat: -30.0277, lon: -51.2287, name: "Porto Alegre", color: "#60a5fa", size: 0.04 },   // Porto Alegre
    { lat: -19.9167, lon: -43.9345, name: "Belo Horizonte", color: "#60a5fa", size: 0.04 }, // Belo Horizonte
    { lat: -12.9714, lon: -38.5014, name: "Salvador", color: "#60a5fa", size: 0.04 },       // Salvador
    { lat: -8.0476, lon: -34.8770, name: "Recife", color: "#60a5fa", size: 0.04 },          // Recife
    { lat: -3.7319, lon: -38.5267, name: "Fortaleza", color: "#60a5fa", size: 0.04 },       // Fortaleza
    { lat: -16.6799, lon: -49.2550, name: "Goiânia", color: "#60a5fa", size: 0.04 },        // Goiânia
    { lat: -3.1190, lon: -60.0217, name: "Manaus", color: "#60a5fa", size: 0.04 },          // Manaus
    { lat: -9.9747, lon: -67.8099, name: "Rio Branco", color: "#60a5fa", size: 0.03 },      // Rio Branco
    { lat: -20.4428, lon: -54.6464, name: "Campo Grande", color: "#60a5fa", size: 0.03 },   // Campo Grande
    { lat: -27.5969, lon: -48.5495, name: "Florianópolis", color: "#60a5fa", size: 0.03 }   // Florianópolis
  ];

  // Converte coordenadas para vetores 3D
  const points3D = connectionPoints.map(point => ({
    ...point,
    position: latLongToVector3(point.lat, point.lon, radius)
  }));

  // Conexões entre pontos (representando fluxo de dados entre filiais no Brasil)
  const connections = [
    { from: 0, to: 1, color: "#60a5fa", width: 2 },   // São Paulo -> Rio de Janeiro
    { from: 0, to: 2, color: "#60a5fa", width: 2 },   // São Paulo -> Brasília
    { from: 0, to: 3, color: "#60a5fa", width: 1.5 }, // São Paulo -> Curitiba
    { from: 0, to: 4, color: "#60a5fa", width: 1.5 }, // São Paulo -> Porto Alegre
    { from: 0, to: 5, color: "#60a5fa", width: 1.5 }, // São Paulo -> Belo Horizonte
    { from: 2, to: 6, color: "#60a5fa", width: 1 },   // Brasília -> Salvador
    { from: 2, to: 7, color: "#60a5fa", width: 1 },   // Brasília -> Recife
    { from: 2, to: 8, color: "#60a5fa", width: 1 },   // Brasília -> Fortaleza
    { from: 2, to: 9, color: "#60a5fa", width: 1 },   // Brasília -> Goiânia
    { from: 2, to: 10, color: "#60a5fa", width: 1 },  // Brasília -> Manaus
    { from: 10, to: 11, color: "#60a5fa", width: 0.5 }, // Manaus -> Rio Branco
    { from: 2, to: 12, color: "#60a5fa", width: 0.5 }, // Brasília -> Campo Grande
    { from: 3, to: 13, color: "#60a5fa", width: 0.5 }  // Curitiba -> Florianópolis
  ];

  // Obtém a posição do Brasil
  const brasilCenter = getBrasilPosition(radius);

  // Log para verificar se os pontos estão sendo criados
  console.log(`Total de pontos: ${points3D.length}`);
  console.log(`Total de conexões: ${connections.length}`);

  return (
    <group>
      {/* Globo principal */}
      <Sphere ref={globeRef} args={[radius, 64, 64]} scale={1.6}>
        <meshStandardMaterial
          map={mapTexture}
          metalness={0.4}
          roughness={0.7}
          side={THREE.FrontSide}
        />
      </Sphere>

      {/* Marcador MUITO visível para o Brasil */}
      <group>
        {/* Grande círculo destacando o Brasil */}
        <mesh position={brasilCenter}>
          <sphereGeometry args={[radius * 0.7, 32, 32]} />
          <meshBasicMaterial color="#60a5fa" transparent opacity={0.3} wireframe />
        </mesh>

        {/* Texto "BRASIL" flutuando acima do país */}
        <Html position={[brasilCenter.x, brasilCenter.y + 0.5, brasilCenter.z]} center>
          <div style={{
            color: 'white',
            background: 'rgba(0,0,255,0.5)',
            padding: '5px 10px',
            borderRadius: '5px',
            fontWeight: 'bold',
            fontSize: '16px',
            textShadow: '0 0 5px blue, 0 0 10px blue'
          }}>
            BRASIL
          </div>
        </Html>

        {/* Luz intensa no Brasil */}
        <pointLight
          position={brasilCenter}
          color="#60a5fa"
          intensity={50}
          distance={3}
        />
      </group>

      {/* Pontos de conexão */}
      {points3D.map((point, index) => (
        <ConnectionPoint
          key={`point-${index}`}
          position={point.position}
          color={point.color}
          size={point.size}
        />
      ))}

      {/* Linhas de conexão */}
      {connections.map((conn, index) => (
        <ConnectionLine
          key={`line-${index}`}
          start={points3D[conn.from].position}
          end={points3D[conn.to].position}
          color={conn.color}
          width={conn.width}
        />
      ))}
    </group>
  );
}

// Pré-carrega a textura
// Atualizado o caminho para pré-carregar a textura correta
useTexture.preload('/textures/mundo1.jpg');

// Coordenadas do Brasil para uso global
const BRASIL_LAT = -15.77972;
const BRASIL_LON = -47.92972;

// Função para obter a posição 3D do Brasil
function getBrasilPosition(radius: number = 1.5): THREE.Vector3 {
  return latLongToVector3(BRASIL_LAT, BRASIL_LON, radius);
}

// Componente Canvas que renderiza a cena do Globo
export function InteractiveGlobe() {
  // Obtém a posição do Brasil para usar nos controles
  const brasilPosition = getBrasilPosition();

  return (
    // Container relativo para ocupar espaço na seção
    <div className="relative h-full w-full opacity-100"> {/* Opacidade máxima */}
      {/* Posição da câmera ajustada para focar no Brasil */}
      <Canvas
        camera={{
          position: [1, -1, 3], // Posição ajustada para focar diretamente no Brasil
          fov: 40, // Campo de visão mais estreito para zoom no Brasil
          near: 0.1,
          far: 1000
        }}
        gl={{
          antialias: true,
          alpha: true,
          logarithmicDepthBuffer: true // Ajuda com problemas de z-fighting
        }}
        dpr={[1, 2]} // Melhor resolução em dispositivos de alta densidade
      >
        {/* Iluminação mais intensa */}
        <ambientLight intensity={Math.PI * 0.7} />
        <directionalLight position={[3, 2, 4]} intensity={2} />
        <pointLight position={[-5, -5, -5]} intensity={50} color="#60a5fa" />
        {/* Luz adicional para destacar o Brasil */}
        <pointLight position={[brasilPosition.x, brasilPosition.y, brasilPosition.z]} intensity={30} color="#ffffff" />
        {/* Luz específica para o Brasil */}
        <spotLight
          position={[brasilPosition.x, brasilPosition.y, brasilPosition.z]}
          angle={0.5}
          penumbra={0.5}
          intensity={100}
          color="#60a5fa"
          distance={10}
          castShadow
        />

        <Suspense fallback={<Html center className="text-blue-200">Carregando Globo...</Html>}>
          <GlobeMesh />
        </Suspense>

        {/* Controles de órbita - habilitados para permitir interação do usuário */}
        <OrbitControls
          enableZoom={true}
          enablePan={false}
          enableRotate={true}
          rotateSpeed={0.5} // Velocidade de rotação mais suave
          zoomSpeed={0.7} // Velocidade de zoom mais suave
          minDistance={2} // Limita o zoom mínimo
          maxDistance={8} // Limita o zoom máximo
          // Posição inicial focada no Brasil
          target={[brasilPosition.x, brasilPosition.y, brasilPosition.z]} // Foco no Brasil
          makeDefault
        />
        <Preload all />
      </Canvas>
    </div>
  );
}
