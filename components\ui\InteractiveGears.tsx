"use client";

import React, { useRef } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, useTexture } from '@react-three/drei';
import * as THREE from 'three';

// Componente para uma única engrenagem
function Gear({ position, rotation, scale = 1, speed = 1, color = "#88aaff", metalness = 0.8, roughness = 0.3 }: { position: [number, number, number], rotation: [number, number, number], scale?: number, speed?: number, color?: string, metalness?: number, roughness?: number }) {
  const meshRef = useRef<THREE.Mesh>(null!);
  const teeth = 12; // Número de dentes
  const radius = 0.5 * scale;
  const thickness = 0.2 * scale;
  const toothHeight = 0.1 * scale;
  const toothWidth = (Math.PI * 2 * radius) / teeth / 2;

  // Criar a forma base da engrenagem (cilindro + dentes)
  const shape = new THREE.Shape();
  const outerRadius = radius + toothHeight;
  const innerRadius = radius;

  for (let i = 0; i < teeth; i++) {
    const angle = (i / teeth) * Math.PI * 2;
    const nextAngle = ((i + 0.5) / teeth) * Math.PI * 2;
    const finalAngle = ((i + 1) / teeth) * Math.PI * 2;

    // Base do dente (arco interno)
    shape.absarc(0, 0, innerRadius, angle, nextAngle, false);
    // Lado do dente (linha até o topo)
    shape.lineTo(Math.cos(nextAngle) * outerRadius, Math.sin(nextAngle) * outerRadius);
    // Topo do dente (arco externo)
    shape.absarc(0, 0, outerRadius, nextAngle, finalAngle, false);
     // Outro lado do dente (linha de volta à base)
    shape.lineTo(Math.cos(finalAngle) * innerRadius, Math.sin(finalAngle) * innerRadius);
  }

  const extrudeSettings = {
    steps: 1,
    depth: thickness,
    bevelEnabled: false,
  };

  const geometry = new THREE.ExtrudeGeometry(shape, extrudeSettings);
  geometry.center(); // Centralizar a geometria

  // Animação de rotação
  useFrame((state, delta) => {
    if (meshRef.current) {
      meshRef.current.rotation.z += delta * speed;
    }
  });

  return (
    <mesh ref={meshRef} position={position} rotation={rotation} geometry={geometry}>
      <meshStandardMaterial
        color={color}
        metalness={metalness}
        roughness={roughness}
        side={THREE.DoubleSide}
      />
    </mesh>
  );
}

// Componente Canvas que renderiza a cena das engrenagens
export function InteractiveGears() {
  return (
    <div className="w-full h-[300px] md:h-[400px] rounded-xl overflow-hidden border border-blue-400/20 shadow-xl shadow-blue-500/10 bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative cursor-grab active:cursor-grabbing">
      <Canvas camera={{ position: [0, 0, 3.5], fov: 50 }}>
        <ambientLight intensity={Math.PI * 0.6} />
        <pointLight position={[5, 5, 5]} intensity={150} color="#ffffff" />
        <pointLight position={[-5, -5, -5]} intensity={80} color="#a78bfa" />
        <directionalLight position={[0, 10, 5]} intensity={2} />

        {/* Conjunto de Engrenagens */}
        <group rotation={[Math.PI / 6, Math.PI / 4, 0]}> {/* Inclina um pouco o grupo */}
          <Gear position={[0, 0, 0]} rotation={[Math.PI / 2, 0, 0]} speed={1} scale={1.2} color="#60a5fa" />
          <Gear position={[0.7, 0.1, 0]} rotation={[Math.PI / 2, 0, Math.PI / teeth]} speed={-1.5} scale={0.8} color="#818cf8" />
          <Gear position={[-0.6, -0.5, 0.1]} rotation={[Math.PI / 2, 0, Math.PI / (teeth / 2)]} speed={2} scale={1} color="#a78bfa" />
           <Gear position={[0.1, 0.8, -0.1]} rotation={[Math.PI / 2, 0, Math.PI / (teeth * 1.5)]} speed={-0.8} scale={0.7} color="#34d399" />
        </group>

        <OrbitControls enableZoom={true} enablePan={true} autoRotate={false} />
      </Canvas>
      <div className="absolute inset-0 bg-gradient-to-t from-blue-900/40 to-transparent pointer-events-none"></div>
      <div className="absolute bottom-2 left-2 text-xs text-blue-300/70 pointer-events-none">
        Clique e arraste para girar
      </div>
    </div>
  );
}

// Definindo 'teeth' fora do componente Gear para ser acessível no Canvas
const teeth = 12;
