import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>R<PERSON>, CheckCircle2 } from "lucide-react";
import { useRef, useEffect, useState } from "react";
import gsap from "gsap";
import Image from "next/image"; // Removido
// import { InteractiveCube } from "../ui/InteractiveCube"; // Comentado
// import { InteractiveGears } from "../ui/InteractiveGears"; // Comentado
// import { InteractiveBear } from "../ui/InteractiveBear"; // Adicionado

// Simplificando os passos de implementação
const implementationSteps = [
  {
    title: "Diagnóstico",
    description: "Avaliamos as necessidades específicas da sua indústria",
  },
  {
    title: "Configuração",
    description: "Parametrizamos o sistema para seu negócio",
  },
  {
    title: "Treinamento",
    description: "Capacitamos sua equipe para uso eficiente",
  },
  {
    title: "Suporte",
    description: "Acompanhamento contínuo para garantir sucesso",
  },
];

export function ImplementationSection() {
  const sectionRef = useRef(null);
  const processItemsRef = useRef<HTMLDivElement>(null);
  const [activeStep, setActiveStep] = useState<number | null>(null);
  const mouseRef = useRef({ x: 0, y: 0 });

  // Efeito para rastrear a posição do mouse
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (sectionRef.current) {
        const rect = (sectionRef.current as HTMLElement).getBoundingClientRect();
        mouseRef.current = {
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        };
      }
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Efeito para animações GSAP
  useEffect(() => {
    const ctx = gsap.context(() => {
      // Animação para o fluxo de implementação em desktop
      gsap.fromTo(
        ".process-line",
        { width: 0 },
        { 
          width: "100%", 
          duration: 1.5,
          ease: "power2.inOut",
          scrollTrigger: {
            trigger: sectionRef.current,
            start: "top 70%",
          }
        }
      );
      
      // Animações para as linhas em mobile (formato grade 2x2)
      // Linhas horizontais
      gsap.fromTo(
        [".process-line-mobile-h-top", ".process-line-mobile-h-bottom"],
        { width: 0 },
        { 
          width: "100%", 
          duration: 1.5,
          ease: "power2.inOut",
          scrollTrigger: {
            trigger: sectionRef.current,
            start: "top 70%",
          }
        }
      );
      
      // Linhas verticais
      gsap.fromTo(
        [".process-line-mobile-v-left", ".process-line-mobile-v-right"],
        { height: 0 },
        { 
          height: "100%", 
          duration: 1.5,
          ease: "power2.inOut",
          scrollTrigger: {
            trigger: sectionRef.current,
            start: "top 70%",
          }
        }
      );
      
      // Animação para os itens do processo
      gsap.fromTo(
        ".process-item",
        { scale: 0.8, opacity: 0 },
        { 
          scale: 1, 
          opacity: 1, 
          stagger: 0.25,
          duration: 0.5,
          ease: "back.out(1.7)",
          scrollTrigger: {
            trigger: sectionRef.current,
            start: "top 70%",
          }
        }
      );
      
      // Animação para o texto principal
      gsap.fromTo(
        ".main-content",
        { y: 30, opacity: 0 },
        { 
          y: 0, 
          opacity: 1,
          duration: 0.8,
          scrollTrigger: {
            trigger: sectionRef.current,
            start: "top 70%",
          }
        }
      );

      // Efeito de paralaxe suave para o fundo
      const bgElements = document.querySelectorAll('.bg-glow');
      bgElements.forEach(el => {
        gsap.to(el, {
          x: "random(-20, 20)",
          y: "random(-20, 20)",
          duration: "random(20, 30)",
          repeat: -1,
          yoyo: true,
          ease: "sine.inOut"
        });
      });
    }, sectionRef);

    return () => ctx.revert();
  }, []);

  // Função para lidar com o hover nos itens do processo
  const handleStepHover = (index: number | null) => {
    setActiveStep(index);
    
    if (index !== null && processItemsRef.current) {
      const items = processItemsRef.current.querySelectorAll('.process-item');
      items.forEach((item, i) => {
        if (i === index) {
          gsap.to(item, { scale: 1.1, duration: 0.3 });
          gsap.to(item.querySelector('.step-number'), { 
            backgroundColor: '#60a5fa', 
            boxShadow: '0 0 15px rgba(96, 165, 250, 0.5)',
            duration: 0.3 
          });
        } else {
          gsap.to(item, { scale: 0.95, opacity: 0.7, duration: 0.3 });
        }
      });
    } else if (processItemsRef.current) {
      const items = processItemsRef.current.querySelectorAll('.process-item');
      items.forEach(item => {
        gsap.to(item, { scale: 1, opacity: 1, duration: 0.3 });
        gsap.to(item.querySelector('.step-number'), { 
          backgroundColor: '', 
          boxShadow: '',
          duration: 0.3 
        });
      });
    }
  };

  return (
    <section
      ref={sectionRef}
      id="implementation"
      className="relative flex min-h-screen items-center justify-center overflow-hidden bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 py-16"
    >
      {/* Background decorative elements with subtle movement */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(120,119,198,0.2),rgba(255,255,255,0))]" />
      <div className="pointer-events-none absolute inset-0">
        <div className="bg-glow absolute -top-40 left-1/4 h-80 w-80 rounded-full bg-blue-500/20 blur-[100px]" />
        <div className="bg-glow absolute -bottom-40 right-1/4 h-80 w-80 rounded-full bg-purple-500/20 blur-[100px]" />
        <div className="bg-glow absolute top-1/3 right-1/3 h-40 w-40 rounded-full bg-indigo-500/10 blur-[80px]" />
      </div>

      <div className="container relative z-10 mx-auto px-4">
        <div className="flex flex-col lg:flex-row gap-12 items-center">
          {/* Lado esquerdo - Conteúdo principal */}
          <div className="lg:w-1/2 main-content">
            {/* <div className="mb-6 inline-flex rounded-full bg-blue-400/10 p-3 hover:bg-blue-400/20 transition-all duration-300 cursor-pointer">
              <ShieldCheck className="h-8 w-8 text-blue-300" />
            </div> */}

            <h2 className="mb-4 bg-gradient-to-r from-white via-white to-white bg-clip-text text-4xl font-bold text-transparent md:text-5xl">
              Implementação sem estresse
            </h2>
            
            <p className="mb-6 text-lg text-blue-200">
              Foque no crescimento do seu negócio enquanto nós cuidamos de toda a
              configuração e implantação do Kodiak.
            </p>
            
            <ul className="mb-8 space-y-3">
              {["Processo 100% gerenciado pela nossa equipe", 
                "Migração de dados com eficiência", 
                "Treinamento completo para sua equipe", 
                "Suporte técnico especializado"].map((item, index) => (
                <li key={index} className="feature-item flex items-start group cursor-pointer">
                  <div className="mr-3 h-6 w-6 flex-shrink-0 rounded-full bg-blue-900 flex items-center justify-center group-hover:bg-blue-700 transition-all duration-300">
                    <CheckCircle2 className="h-5 w-5 text-blue-400 group-hover:text-blue-300 transition-all duration-300" />
                  </div>
                  <span className="text-blue-100 group-hover:text-white transition-all duration-300">{item}</span>
                </li>
              ))}
            </ul>
            
            <a
            target="_blank"
            href="https://wa.me/5519989386246?text=Olá!%20Gostaria%20de%20saber%20mais%20sobre%20o%20Kodiak%20ERP."
            className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white font-medium py-3 px-8 rounded-lg transition-all shadow-lg shadow-blue-500/30 transform hover:scale-105 relative overflow-hidden group"
          >
            <span className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-white/0 via-white/20 to-white/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />
            <span className="relative z-10">
              Agende uma demonstração
              <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform inline-block ml-1" />
            </span>
          </a>
          </div>
          
          {/* Lado direito - Processo visual interativo */}
          <div className="lg:w-1/2 relative">
            <div className="relative mx-auto max-w-md">
              {/* Linha de processo */}
              <div className="absolute top-6 left-0 right-0 h-1 bg-blue-900/50 transform rounded-full overflow-hidden hidden md:block">
                <div className="process-line h-full bg-gradient-to-r from-blue-400 to-purple-400"></div>
              </div>

              {/* Linha de processo para mobile - vertical entre os itens */}
              <div className="absolute top-0 bottom-0 left-1/2 w-1 bg-blue-900/50 transform -translate-x-1/2 rounded-full overflow-hidden md:hidden">
                <div className="process-line-mobile h-0 bg-gradient-to-b from-blue-400 to-purple-400"></div>
              </div>
              
              {/* Itens do processo interativos */}
              <div ref={processItemsRef} className="relative grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-2">
                {implementationSteps.map((step, index) => (
                  <div 
                    key={index} 
                    className="process-item relative z-10 flex flex-col items-center cursor-pointer mb-8"
                    onMouseEnter={() => handleStepHover(index)}
                    onMouseLeave={() => handleStepHover(null)}
                  >
                    <div className="step-number mb-2 h-12 w-12 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center shadow-lg shadow-blue-500/20 transition-all duration-300">
                      <span className="font-bold text-white">{index + 1}</span>
                    </div>
                    <div className="w-32 text-center">
                      <h4 className="font-semibold text-white mb-1">{step.title}</h4>
                      <p className="text-xs text-blue-200">{step.description}</p>
                    </div>
                  </div>
                ))}
              </div>

              {/* Linha de processo para desktop - horizontal */}
              <div className="absolute top-6 left-0 right-0 h-1 bg-blue-900/50 transform rounded-full overflow-hidden hidden md:block">
                <div className="process-line h-full bg-gradient-to-r from-blue-400 to-purple-400"></div>
              </div>
              
              {/* Ilustração com efeito de profundidade */}
              <div className="relative transform perspective-1000">
                <div
                  className="rounded-xl overflow-hidden border border-blue-400/20 shadow-xl shadow-blue-500/10 transition-transform duration-300 hover:scale-[1.02]"
                  style={{
                    transformStyle: 'preserve-3d',
                  }}
                >
                  <Image
                    src="/canvas/2.avif"
                    alt="Implementação Kodiak"
                    width={500}
                    height={300}
                    className="w-full h-auto object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-blue-900/40 to-transparent"></div>
                </div>
                <div className="absolute -bottom-4 -right-4 bg-blue-900/80 backdrop-blur-sm p-4 rounded-lg border border-blue-400/30 transform hover:scale-105 transition-transform duration-300 cursor-pointer">
                <p className="text-1xl text-white font-semibold">Nossa equipe cuidando </p>
                  <p className="text-1xl font-bold text-white">do seu negócio.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
