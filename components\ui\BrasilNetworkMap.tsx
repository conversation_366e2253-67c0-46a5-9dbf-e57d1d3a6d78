"use client";

import React, { useRef, useState, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, useTexture, Html, Preload } from '@react-three/drei';
import * as THREE from 'three';
import { useInView } from 'framer-motion';

// Componente para um ponto de luz na rede
function NetworkPoint({ 
  position, 
  color = "#60a5fa", 
  size = 0.05, 
  delay = 0,
  onReady = () => {},
  isRoot = false
}: { 
  position: THREE.Vector3, 
  color?: string, 
  size?: number,
  delay?: number,
  onReady?: () => void,
  isRoot?: boolean
}) {
  const [visible, setVisible] = useState(isRoot);
  const [scale, setScale] = useState(isRoot ? 1 : 0);
  const meshRef = useRef<THREE.Mesh>(null!);
  
  // Efeito para animar o aparecimento do ponto após o delay
  useEffect(() => {
    if (isRoot) {
      onReady();
      return;
    }
    
    const timer = setTimeout(() => {
      setVisible(true);
      
      // Anima o crescimento do ponto
      const growInterval = setInterval(() => {
        setScale(prev => {
          const newScale = prev + 0.1;
          if (newScale >= 1) {
            clearInterval(growInterval);
            onReady(); // Notifica que o ponto está pronto para criar conexões
            return 1;
          }
          return newScale;
        });
      }, 50);
      
      return () => clearInterval(growInterval);
    }, delay);
    
    return () => clearTimeout(timer);
  }, [delay, isRoot, onReady]);
  
  // Efeito de pulsação para os pontos
  useFrame(({ clock }) => {
    if (meshRef.current && visible) {
      const pulse = 1 + Math.sin(clock.getElapsedTime() * 2) * 0.2;
      const currentScale = scale * pulse;
      meshRef.current.scale.set(currentScale, currentScale, currentScale);
    }
  });
  
  if (!visible) return null;
  
  return (
    <group position={position}>
      {/* Ponto principal */}
      <mesh ref={meshRef}>
        <sphereGeometry args={[size, 16, 16]} />
        <meshStandardMaterial
          color={color}
          emissive={color}
          emissiveIntensity={5}
          toneMapped={false}
        />
      </mesh>
      
      {/* Halo ao redor do ponto */}
      <mesh scale={[1.5, 1.5, 1.5]}>
        <sphereGeometry args={[size, 16, 16]} />
        <meshBasicMaterial
          color={color}
          transparent={true}
          opacity={0.3}
          toneMapped={false}
        />
      </mesh>
      
      {/* Luz pontual */}
      <pointLight color={color} intensity={5} distance={1} />
    </group>
  );
}

// Componente para uma conexão entre dois pontos
function NetworkConnection({ 
  start, 
  end, 
  color = "#60a5fa", 
  width = 0.01,
  progress = 0
}: { 
  start: THREE.Vector3, 
  end: THREE.Vector3, 
  color?: string,
  width?: number,
  progress?: number
}) {
  const lineRef = useRef<THREE.Mesh>(null!);
  
  // Calcula o ponto intermediário com base no progresso
  const getIntermediatePoint = () => {
    const direction = new THREE.Vector3().subVectors(end, start);
    return new THREE.Vector3().addVectors(start, direction.multiplyScalar(progress));
  };
  
  // Cria a curva da linha
  const points = [start, getIntermediatePoint()];
  const curve = new THREE.CatmullRomCurve3(points);
  
  // Cria a geometria do tubo
  const tubeGeometry = new THREE.TubeGeometry(curve, 64, width, 8, false);
  
  return (
    <mesh ref={lineRef} geometry={tubeGeometry}>
      <meshStandardMaterial
        color={color}
        emissive={color}
        emissiveIntensity={2}
        transparent={true}
        opacity={0.8}
      />
    </mesh>
  );
}

// Componente principal do mapa de rede
function NetworkMap() {
  const mapRef = useRef<THREE.Mesh>(null!);
  const [activeConnections, setActiveConnections] = useState<{
    start: THREE.Vector3;
    end: THREE.Vector3;
    progress: number;
    color: string;
    width: number;
    id: string;
  }[]>([]);
  
  // Textura do mapa do Brasil (escura)
  const mapTexture = useTexture('/textures/brasil_map_dark.png');
  
  // Pontos iniciais da rede (coordenadas no plano)
  const rootPoint = new THREE.Vector3(0, 0, 0.01); // Ponto central levemente acima do plano
  
  // Pontos de nível 1 (primeiras conexões)
  const level1Points = [
    { pos: new THREE.Vector3(-0.3, 0.2, 0.01), color: "#60a5fa" },
    { pos: new THREE.Vector3(0.3, 0.3, 0.01), color: "#818cf8" },
    { pos: new THREE.Vector3(0.4, -0.2, 0.01), color: "#a78bfa" },
    { pos: new THREE.Vector3(-0.2, -0.3, 0.01), color: "#34d399" },
    { pos: new THREE.Vector3(0, 0.4, 0.01), color: "#fbbf24" }
  ];
  
  // Pontos de nível 2 (conexões secundárias)
  const level2Points = [
    // A partir do ponto 0 de nível 1
    { parent: 0, pos: new THREE.Vector3(-0.5, 0.4, 0.01), color: "#60a5fa" },
    { parent: 0, pos: new THREE.Vector3(-0.6, 0.1, 0.01), color: "#60a5fa" },
    
    // A partir do ponto 1 de nível 1
    { parent: 1, pos: new THREE.Vector3(0.5, 0.5, 0.01), color: "#818cf8" },
    { parent: 1, pos: new THREE.Vector3(0.7, 0.2, 0.01), color: "#818cf8" },
    
    // A partir do ponto 2 de nível 1
    { parent: 2, pos: new THREE.Vector3(0.6, -0.4, 0.01), color: "#a78bfa" },
    { parent: 2, pos: new THREE.Vector3(0.5, -0.6, 0.01), color: "#a78bfa" },
    
    // A partir do ponto 3 de nível 1
    { parent: 3, pos: new THREE.Vector3(-0.4, -0.5, 0.01), color: "#34d399" },
    { parent: 3, pos: new THREE.Vector3(-0.6, -0.3, 0.01), color: "#34d399" },
    
    // A partir do ponto 4 de nível 1
    { parent: 4, pos: new THREE.Vector3(-0.2, 0.6, 0.01), color: "#fbbf24" },
    { parent: 4, pos: new THREE.Vector3(0.2, 0.6, 0.01), color: "#fbbf24" }
  ];
  
  // Estado para controlar quais pontos estão ativos
  const [activeLevel1Points, setActiveLevel1Points] = useState<number[]>([]);
  const [activeLevel2Points, setActiveLevel2Points] = useState<number[]>([]);
  
  // Função para iniciar a animação da rede
  const startNetworkAnimation = () => {
    console.log("Iniciando animação da rede");
  };
  
  // Função chamada quando o ponto raiz está pronto
  const handleRootPointReady = () => {
    console.log("Ponto raiz pronto");
    
    // Inicia as conexões para os pontos de nível 1 com intervalos
    level1Points.forEach((_, index) => {
      setTimeout(() => {
        // Adiciona a conexão com animação
        const connectionId = `root-to-${index}`;
        setActiveConnections(prev => [
          ...prev, 
          { 
            start: rootPoint, 
            end: level1Points[index].pos, 
            progress: 0, 
            color: level1Points[index].color, 
            width: 0.01,
            id: connectionId
          }
        ]);
        
        // Anima o progresso da conexão
        let progress = 0;
        const animateInterval = setInterval(() => {
          progress += 0.05;
          if (progress >= 1) {
            clearInterval(animateInterval);
            // Ativa o ponto de nível 1
            setActiveLevel1Points(prev => [...prev, index]);
          } else {
            setActiveConnections(prev => 
              prev.map(conn => 
                conn.id === connectionId 
                  ? { ...conn, progress } 
                  : conn
              )
            );
          }
        }, 50);
      }, index * 500); // Intervalo entre cada conexão
    });
  };
  
  // Função chamada quando um ponto de nível 1 está pronto
  const handleLevel1PointReady = (index: number) => {
    console.log(`Ponto de nível 1 (${index}) pronto`);
    
    // Encontra os pontos de nível 2 que se conectam a este ponto de nível 1
    const childPoints = level2Points.filter(p => p.parent === index);
    
    // Inicia as conexões para os pontos de nível 2
    childPoints.forEach((point, childIndex) => {
      setTimeout(() => {
        // Adiciona a conexão com animação
        const connectionId = `level1-${index}-to-level2-${level2Points.indexOf(point)}`;
        setActiveConnections(prev => [
          ...prev, 
          { 
            start: level1Points[index].pos, 
            end: point.pos, 
            progress: 0, 
            color: point.color, 
            width: 0.008,
            id: connectionId
          }
        ]);
        
        // Anima o progresso da conexão
        let progress = 0;
        const animateInterval = setInterval(() => {
          progress += 0.05;
          if (progress >= 1) {
            clearInterval(animateInterval);
            // Ativa o ponto de nível 2
            setActiveLevel2Points(prev => [...prev, level2Points.indexOf(point)]);
          } else {
            setActiveConnections(prev => 
              prev.map(conn => 
                conn.id === connectionId 
                  ? { ...conn, progress } 
                  : conn
              )
            );
          }
        }, 50);
      }, childIndex * 300); // Intervalo entre cada conexão
    });
  };
  
  return (
    <group>
      {/* Plano com a textura do mapa do Brasil */}
      <mesh 
        ref={mapRef} 
        rotation={[0, 0, 0]} 
        position={[0, 0, 0]}
      >
        <planeGeometry args={[2, 2]} />
        <meshBasicMaterial 
          map={mapTexture} 
          transparent={true} 
          opacity={0.8}
        />
      </mesh>
      
      {/* Ponto raiz (inicial) */}
      <NetworkPoint 
        position={rootPoint} 
        color="#f43f5e" 
        size={0.03} 
        isRoot={true}
        onReady={handleRootPointReady}
      />
      
      {/* Pontos de nível 1 */}
      {level1Points.map((point, index) => (
        activeLevel1Points.includes(index) && (
          <NetworkPoint 
            key={`level1-${index}`}
            position={point.pos} 
            color={point.color} 
            size={0.025}
            delay={100}
            onReady={() => handleLevel1PointReady(index)}
          />
        )
      ))}
      
      {/* Pontos de nível 2 */}
      {level2Points.map((point, index) => (
        activeLevel2Points.includes(index) && (
          <NetworkPoint 
            key={`level2-${index}`}
            position={point.pos} 
            color={point.color} 
            size={0.02}
            delay={100}
          />
        )
      ))}
      
      {/* Conexões */}
      {activeConnections.map((conn) => (
        <NetworkConnection 
          key={conn.id}
          start={conn.start} 
          end={conn.end} 
          color={conn.color}
          width={conn.width}
          progress={conn.progress}
        />
      ))}
    </group>
  );
}

// Componente principal exportado
export function BrasilNetworkMap() {
  const containerRef = useRef(null);
  const isInView = useInView(containerRef, { once: false, amount: 0.3 });
  
  return (
    <div ref={containerRef} className="w-full h-full">
      <Canvas
        camera={{ position: [0, 0, 1.5], fov: 50 }}
        gl={{ 
          antialias: true,
          alpha: true
        }}
        dpr={[1, 2]}
      >
        <ambientLight intensity={0.5} />
        <spotLight position={[0, 0, 2]} intensity={0.8} />
        
        {isInView && (
          <>
            <NetworkMap />
            <OrbitControls 
              enableZoom={false}
              enablePan={false}
              enableRotate={false}
            />
          </>
        )}
        
        <Preload all />
      </Canvas>
    </div>
  );
}
