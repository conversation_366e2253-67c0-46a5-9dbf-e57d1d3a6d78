{"name": "nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "init-db": "node scripts/init-db.js", "seed-db": "node scripts/seed-db.js"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@next/swc-wasm-nodejs": "13.5.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@react-three/drei": "^9.108.3", "@react-three/fiber": "^8.16.8", "@types/node": "20.6.2", "@types/react": "18.2.22", "@types/react-dom": "18.2.7", "autoplay": "^0.0.4", "autoprefixer": "10.4.15", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-autoplay": "^8.0.0", "embla-carousel-react": "^8.3.0", "framer-motion": "^11.0.3", "gsap": "^3.12.5", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.446.0", "next": "13.5.1", "next-themes": "^0.3.0", "node-fetch": "^3.3.2", "openai": "^4.83.0", "pg": "^8.11.3", "postcss": "8.4.30", "react": "18.2.0", "react-day-picker": "^9.5.1", "react-dom": "18.2.0", "react-hook-form": "^7.48.2", "react-icons": "^5.4.0", "react-markdown": "^9.0.3", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.1", "scrolltrigger": "^1.0.1", "sonner": "^1.7.4", "swiper": "^11.0.5", "tailwind-merge": "^2.5.2", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.7", "three": "^0.159.0", "vaul": "^1.1.2", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/pg": "^8.10.9", "typescript": "5.2.2"}}