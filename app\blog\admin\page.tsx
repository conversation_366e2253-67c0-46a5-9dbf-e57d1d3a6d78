'use client';

import { useState } from 'react';
import { Header } from '@/components/ui/header';
import { Footer } from '@/components/ui/footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { X, Plus } from 'lucide-react';
import { toast } from 'sonner';
import { AuthProvider, useAuth } from '@/hooks/use-auth';

function AdminContent() {
  const { user, loading } = useAuth();
  const [formData, setFormData] = useState({
    title: '',
    excerpt: '',
    iframe_url: '',
    featured_image: '',
    tags: [] as string[],
    published: false
  });
  const [newTag, setNewTag] = useState('');
  const [submitting, setSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      toast.error('Título é obrigatório');
      return;
    }

    setSubmitting(true);

    try {
      const response = await fetch('/api/blog/posts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Post criado com sucesso!');
        setFormData({
          title: '',
          excerpt: '',
          iframe_url: '',
          featured_image: '',
          tags: [],
          published: false
        });
      } else {
        toast.error(data.message || 'Erro ao criar post');
      }
    } catch (error) {
      console.error('Error creating post:', error);
      toast.error('Erro ao criar post');
    } finally {
      setSubmitting(false);
    }
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData({
        ...formData,
        tags: [...formData.tags, newTag.trim()]
      });
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData({
      ...formData,
      tags: formData.tags.filter(tag => tag !== tagToRemove)
    });
  };

  if (loading) {
    return (
      <div className="flex min-h-screen flex-col bg-gradient-to-br from-gray-50 via-white to-gray-100">
        <Header />
        <main className="flex-1 flex items-center justify-center">
          <div>Carregando...</div>
        </main>
        <Footer />
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex min-h-screen flex-col bg-gradient-to-br from-gray-50 via-white to-gray-100">
        <Header />
        <main className="flex-1 container mx-auto px-4 py-20 text-center">
          <h1 className="text-4xl font-bold mb-4">Acesso Restrito</h1>
          <p className="text-gray-600 mb-8">Você precisa estar logado para acessar esta página.</p>
          <div className="flex gap-4 justify-center">
            <Button onClick={() => window.location.href = '/blog/login'}>
              Fazer Login
            </Button>
            <Button variant="outline" onClick={() => window.location.href = '/blog'}>
              Voltar ao Blog
            </Button>
          </div>
          <div className="mt-6 p-4 bg-blue-50 rounded-lg border inline-block">
            <p className="text-sm text-blue-800">
              <strong>Conta de teste:</strong> <EMAIL> / admin123
            </p>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col bg-gradient-to-br from-gray-50 via-white to-gray-100">
      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold mb-8">Criar Nova Notícia</h1>
          
          <Card>
            <CardHeader>
              <CardTitle>Informações da Notícia</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Título */}
                <div>
                  <Label htmlFor="title">Título *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    placeholder="Ex: Produtividade da Indústria em 2025"
                    required
                  />
                </div>

                {/* Resumo */}
                <div>
                  <Label htmlFor="excerpt">Resumo</Label>
                  <Textarea
                    id="excerpt"
                    value={formData.excerpt}
                    onChange={(e) => setFormData({ ...formData, excerpt: e.target.value })}
                    placeholder="Breve descrição da notícia..."
                    rows={3}
                  />
                </div>

                {/* URL do Iframe (Gamma) */}
                <div>
                  <Label htmlFor="iframe_url">URL do Gamma (Iframe) *</Label>
                  <Input
                    id="iframe_url"
                    value={formData.iframe_url}
                    onChange={(e) => setFormData({ ...formData, iframe_url: e.target.value })}
                    placeholder="https://gamma.app/embed/seu-id-aqui"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Cole aqui a URL de embed do Gamma. Ex: https://gamma.app/embed/0oy42pmm78wcnb2
                  </p>
                </div>

                {/* Imagem Destacada */}
                <div>
                  <Label htmlFor="featured_image">URL da Imagem Destacada</Label>
                  <Input
                    id="featured_image"
                    value={formData.featured_image}
                    onChange={(e) => setFormData({ ...formData, featured_image: e.target.value })}
                    placeholder="https://exemplo.com/imagem.jpg"
                  />
                </div>

                {/* Tags */}
                <div>
                  <Label>Tags</Label>
                  <div className="flex gap-2 mb-2">
                    <Input
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      placeholder="Digite uma tag"
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                    />
                    <Button type="button" onClick={addTag} size="sm">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                        {tag}
                        <X 
                          className="h-3 w-3 cursor-pointer" 
                          onClick={() => removeTag(tag)}
                        />
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Publicar */}
                <div className="flex items-center space-x-2">
                  <Switch
                    id="published"
                    checked={formData.published}
                    onCheckedChange={(checked) => setFormData({ ...formData, published: checked })}
                  />
                  <Label htmlFor="published">Publicar imediatamente</Label>
                </div>

                {/* Botões */}
                <div className="flex gap-4">
                  <Button type="submit" disabled={submitting}>
                    {submitting ? 'Criando...' : 'Criar Notícia'}
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline"
                    onClick={() => window.location.href = '/blog'}
                  >
                    Cancelar
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>

          {/* Instruções */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>📋 Como Criar uma Notícia</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold">1. Criar Apresentação no Gamma</h4>
                <p className="text-sm text-gray-600">
                  Acesse <a href="https://gamma.app" target="_blank" className="text-blue-600 hover:underline">gamma.app</a> e crie sua apresentação sobre a notícia.
                </p>
              </div>
              <div>
                <h4 className="font-semibold">2. Obter URL de Embed</h4>
                <p className="text-sm text-gray-600">
                  No Gamma, clique em "Share" → "Embed" e copie a URL que aparece no src do iframe.
                </p>
              </div>
              <div>
                <h4 className="font-semibold">3. Preencher Formulário</h4>
                <p className="text-sm text-gray-600">
                  Cole a URL do Gamma no campo "URL do Gamma", adicione título, resumo e tags.
                </p>
              </div>
              <div>
                <h4 className="font-semibold">4. Publicar</h4>
                <p className="text-sm text-gray-600">
                  Ative "Publicar imediatamente" e clique em "Criar Notícia".
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>

      <Footer />
    </div>
  );
}

export default function AdminPage() {
  return (
    <AuthProvider>
      <AdminContent />
    </AuthProvider>
  );
}
