const { Pool } = require('pg');
require('dotenv').config({ path: '.env.local' });

const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || '**************',
  database: process.env.DB_NAME || 'blog_bruno',
  password: process.env.DB_PASSWORD || '123!asd',
  port: parseInt(process.env.DB_PORT || '5432'),
});

async function initDatabase() {
  const client = await pool.connect();

  try {
    console.log('🚀 Inicializando banco de dados...');

    // Criar tabela users
    await client.query(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        name VARCHAR(255),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);
    console.log('✅ Tabela users criada');

    // Criar tabela blog_posts
    await client.query(`
      CREATE TABLE IF NOT EXISTS blog_posts (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        title VARCHAR(500) NOT NULL,
        slug VARCHAR(500) UNIQUE NOT NULL,
        content TEXT,
        iframe_url TEXT,
        author_id UUID REFERENCES users(id) ON DELETE CASCADE,
        published BOOLEAN DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        views INTEGER DEFAULT 0,
        featured_image TEXT,
        excerpt TEXT,
        tags TEXT[]
      );
    `);
    console.log('✅ Tabela blog_posts criada');

    // Criar tabela post_likes
    await client.query(`
      CREATE TABLE IF NOT EXISTS post_likes (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        post_id UUID REFERENCES blog_posts(id) ON DELETE CASCADE,
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(post_id, user_id)
      );
    `);
    console.log('✅ Tabela post_likes criada');

    // Criar tabela newsletter_subscribers
    await client.query(`
      CREATE TABLE IF NOT EXISTS newsletter_subscribers (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        subscribed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        active BOOLEAN DEFAULT true
      );
    `);
    console.log('✅ Tabela newsletter_subscribers criada');

    // Criar tabela post_shares
    await client.query(`
      CREATE TABLE IF NOT EXISTS post_shares (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        post_id UUID REFERENCES blog_posts(id) ON DELETE CASCADE,
        platform VARCHAR(50) NOT NULL,
        shared_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);
    console.log('✅ Tabela post_shares criada');

    // Criar índices para performance
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_blog_posts_published ON blog_posts(published);
      CREATE INDEX IF NOT EXISTS idx_blog_posts_created_at ON blog_posts(created_at DESC);
      CREATE INDEX IF NOT EXISTS idx_blog_posts_views ON blog_posts(views DESC);
      CREATE INDEX IF NOT EXISTS idx_blog_posts_slug ON blog_posts(slug);
      CREATE INDEX IF NOT EXISTS idx_post_likes_post_id ON post_likes(post_id);
      CREATE INDEX IF NOT EXISTS idx_post_likes_user_id ON post_likes(user_id);
    `);
    console.log('✅ Índices criados');

    console.log('🎉 Banco de dados inicializado com sucesso!');
    console.log('\n📝 Próximos passos:');
    console.log('1. Configure as variáveis de ambiente no arquivo .env.local');
    console.log('2. Execute: npm run dev');
    console.log('3. Acesse: http://localhost:3000/blog');
  } catch (error) {
    console.error('❌ Erro ao inicializar banco de dados:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  initDatabase().catch(console.error);
}

module.exports = { initDatabase };
