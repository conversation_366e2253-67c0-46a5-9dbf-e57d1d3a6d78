'use client';

import { useState } from 'react';
import { BlogPost } from '@/types/blog';
import { Button } from '@/components/ui/button';
import { Heart } from 'lucide-react';
import { useAuth } from '@/hooks/use-auth';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface BlogLikeProps {
  post: BlogPost;
  onUpdate: (post: BlogPost) => void;
}

export function BlogLike({ post, onUpdate }: BlogLikeProps) {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);

  const handleLike = async () => {
    if (!user) {
      toast.error('Faça login para curtir posts');
      return;
    }

    setLoading(true);

    try {
      const response = await fetch(`/api/blog/posts/${post.slug}/like`, {
        method: 'POST',
      });

      const data = await response.json();

      if (data.success) {
        // Atualizar o post com os novos dados
        onUpdate({
          ...post,
          is_liked: data.data.is_liked,
          likes_count: data.data.likes_count,
        });
        
        toast.success(data.message);
      } else {
        toast.error(data.message);
      }
    } catch (error) {
      console.error('Error toggling like:', error);
      toast.error('Erro ao curtir post');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleLike}
      disabled={loading}
      className={cn(
        "flex items-center gap-2 transition-colors",
        post.is_liked && "bg-red-50 border-red-200 text-red-600 hover:bg-red-100"
      )}
    >
      <Heart 
        className={cn(
          "h-4 w-4 transition-colors",
          post.is_liked && "fill-current"
        )} 
      />
      <span>{post.likes_count || 0}</span>
      <span className="hidden sm:inline">
        {post.is_liked ? 'Curtido' : 'Curtir'}
      </span>
    </Button>
  );
}
