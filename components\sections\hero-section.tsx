"use client";

import Image from "next/image";
import { Button } from "@/components/ui/button";
// import { InteractiveGlobe } from "../ui/InteractiveGlobe"; // Removido daqui

export function HeroSection() {
  return (
    <section className="relative min-h-[95vh] w-full"> {/* Changed h-[95vh] to min-h-[95vh] */}
      <Image
        src="/hero3.jpg"
        alt="Industrial Background"
        fill
        className="object-cover brightness-50"
        priority
        fetchPriority="high"
        sizes="(max-width: 768px) 100vw, 100vw"
        loading="eager"
        placeholder="blur"
        blurDataURL="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgdmlld0JveD0iMCAwIDQwIDQwIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMzMzMzMzIi8+PC9zdmc+"
      />
      {/* Globo 3D removido daqui */}
      <div className="absolute inset-0 flex items-center justify-right pt-20 lg:pt-0"> {/* Changed pt-20 to pt-20 lg:pt-0 */}
        <div className="container text-white"> {/* Removido relative z-10 */}
          <h1 className="hero-title mb-6 text-5xl font-bold leading-tight md:text-6xl">
            Gestão de alto nível
            <br></br> com o 
            <span className="bg-gradient-to-r from-blue-400 to-indigo-400 bg-clip-text text-transparent">
              {" "}
              Kodiak ERP!
            </span>
          </h1>
          <p className="hero-description text-lg text-blue-200 md:text-xl mb-4">
            Automação, controle e eficiência para impulsionar seu negócio.
          </p>
          <div className="hero-cta flex flex-col gap-4 sm:flex-row ">
          <a
            target="_blank"
            href="https://wa.me/5519989386246?text=Olá!%20Gostaria%20de%20saber%20mais%20sobre%20o%20Kodiak%20ERP."
            className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white font-medium py-3 px-8 rounded-lg transition-all shadow-lg shadow-blue-500/30 transform hover:scale-105 relative overflow-hidden group"
          >
            <span className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-white/0 via-white/20 to-white/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />
            <span className="relative z-10">
              Agende uma demonstração
            </span>
          </a>
            <Button
              size="lg"
              variant="outline"
              className="bg-primary text-white hover:bg-white/40"
              onClick={() => {
                const modulesSection = document.getElementById("modules");
                if (modulesSection) {
                  modulesSection.scrollIntoView({
                    behavior: "smooth",
                    block: "start",
                  });
                }
              }}
            >
              Conheça os Módulos
            </Button>
          </div>
        </div>
      </div>
      
      {/* Seta animada para scroll */}
      <div className="absolute bottom-8 left-1/2 animate-bounce hidden lg:block">
        <div 
          className="flex flex-col items-center cursor-pointer"
          onClick={() => {
            const modulesSection = document.getElementById("modules");
            if (modulesSection) {
              modulesSection.scrollIntoView({
                behavior: "smooth",
                block: "start",
              });
            }
          }}
        >
          <span className="text-white text-sm mb-2">Conheça mais</span>
          <div className="w-8 h-8 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
              <path d="M12 5v14M5 12l7 7 7-7"/>
            </svg>
          </div>
        </div>
      </div>
    </section>
  );
}
